package com.ascent.solidus.services.tenant;

import com.ascent.solidus.core.constants.RoleName;
import com.ascent.solidus.core.domain.module.AppUser;
import com.ascent.solidus.core.domain.tenant.Feature;
import com.ascent.solidus.core.domain.tenant.PermissionAction;
import com.ascent.solidus.core.domain.tenant.RoleFeaturePermission;
import com.ascent.solidus.core.domain.umgmt.RoleAssignment;
import com.ascent.solidus.core.repository.umgmt.RoleAssignmentRepository;
import com.ascent.solidus.services.umgmt.AppUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Service for controlling access to features based on tenant assignments and user roles
 */
@Service
public class FeatureAccessControlService {
    
    @Autowired
    private TenantFeatureService tenantFeatureService;
    
    @Autowired
    private RoleFeaturePermissionService roleFeaturePermissionService;
    
    @Autowired
    private TenantService tenantService;
    
    @Autowired
    private AppUserService appUserService;
    
    @Autowired
    private RoleAssignmentRepository roleAssignmentRepository;
    
    /**
     * Check if a user has access to a specific feature
     */
    @Transactional(readOnly = true)
    public boolean hasUserAccessToFeature(Long userId, String featureId) {
        AppUser user = appUserService.findById(userId);
        Long tenantId = user.getTenant().getId();
        
        // First check if tenant has access to the feature
        if (!tenantFeatureService.hasTenantAccessToFeature(tenantId, featureId)) {
            return false;
        }
        
        // Then check if user's roles have permission to access the feature
        List<RoleName> userRoles = getUserRoles(userId);
        return userRoles.stream()
                .anyMatch(role -> hasRoleAccessToFeature(role, featureId));
    }
    
    /**
     * Check if a user has specific permission for a feature
     */
    @Transactional(readOnly = true)
    public boolean hasUserPermissionForFeature(Long userId, String featureId, PermissionAction permission) {
        AppUser user = appUserService.findById(userId);
        Long tenantId = user.getTenant().getId();
        
        // First check if tenant has access to the feature
        if (!tenantFeatureService.hasTenantAccessToFeature(tenantId, featureId)) {
            return false;
        }
        
        // Then check if user's roles have the specific permission
        List<RoleName> userRoles = getUserRoles(userId);
        return userRoles.stream()
                .anyMatch(role -> roleFeaturePermissionService.hasRolePermissionForFeature(role, featureId, permission));
    }
    
    /**
     * Check if a user can access a specific module within a feature
     */
    @Transactional(readOnly = true)
    public boolean canUserAccessModule(Long userId, String featureId, String moduleName) {
        AppUser user = appUserService.findById(userId);
        Long tenantId = user.getTenant().getId();
        
        // First check if tenant has access to the feature
        if (!tenantFeatureService.hasTenantAccessToFeature(tenantId, featureId)) {
            return false;
        }
        
        // Then check if user's roles can access the specific module
        List<RoleName> userRoles = getUserRoles(userId);
        return userRoles.stream()
                .anyMatch(role -> roleFeaturePermissionService.canRoleAccessModule(role, featureId, moduleName));
    }
    
    /**
     * Get all features accessible to a user
     */
    @Transactional(readOnly = true)
    public List<Feature> getUserAccessibleFeatures(Long userId) {
        AppUser user = appUserService.findById(userId);
        Long tenantId = user.getTenant().getId();
        
        // Get tenant's features
        List<Feature> tenantFeatures = tenantFeatureService.getTenantFeatures(tenantId);
        
        // Filter by user's role permissions
        List<RoleName> userRoles = getUserRoles(userId);
        
        return tenantFeatures.stream()
                .filter(feature -> userRoles.stream()
                        .anyMatch(role -> hasRoleAccessToFeature(role, feature.getFeatureId())))
                .collect(Collectors.toList());
    }
    
    /**
     * Get user's roles
     */
    private List<RoleName> getUserRoles(Long userId) {
        List<RoleAssignment> roleAssignments = roleAssignmentRepository.findAllActiveRolesByUserId(userId);
        return roleAssignments.stream()
                .map(ra -> ra.getRole().getName())
                .collect(Collectors.toList());
    }
    
    /**
     * Check if a role has access to a feature
     */
    private boolean hasRoleAccessToFeature(RoleName roleName, String featureId) {
        return roleFeaturePermissionService.getRoleFeaturePermission(roleName, featureId).isPresent();
    }
    
    /**
     * Get user's permissions for a specific feature
     */
    @Transactional(readOnly = true)
    public List<PermissionAction> getUserPermissionsForFeature(Long userId, String featureId) {
        List<RoleName> userRoles = getUserRoles(userId);
        
        return userRoles.stream()
                .map(role -> roleFeaturePermissionService.getRoleFeaturePermission(role, featureId))
                .filter(opt -> opt.isPresent())
                .map(opt -> opt.get())
                .flatMap(permission -> permission.getPermissions().stream())
                .distinct()
                .collect(Collectors.toList());
    }
    
    /**
     * Get user's accessible modules for a specific feature
     */
    @Transactional(readOnly = true)
    public List<String> getUserAccessibleModules(Long userId, String featureId) {
        List<RoleName> userRoles = getUserRoles(userId);
        
        return userRoles.stream()
                .map(role -> roleFeaturePermissionService.getRoleFeaturePermission(role, featureId))
                .filter(opt -> opt.isPresent())
                .map(opt -> opt.get())
                .flatMap(permission -> permission.getAccessibleModules().stream())
                .distinct()
                .collect(Collectors.toList());
    }
}
