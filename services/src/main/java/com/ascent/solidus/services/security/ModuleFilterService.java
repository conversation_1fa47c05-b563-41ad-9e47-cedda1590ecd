package com.ascent.solidus.services.security;

import com.ascent.solidus.core.domain.tenant.Feature;
import com.ascent.solidus.core.domain.tenant.PermissionAction;
import com.ascent.solidus.services.tenant.FeatureAccessControlService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * Service for filtering modules based on user access permissions
 */
@Service
public class ModuleFilterService {
    
    @Autowired
    private FeatureAccessControlService featureAccessControlService;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * Filter modules JSON based on user's feature access
     */
    public JsonNode filterModulesForUser(Long userId, JsonNode modulesJson) {
        ObjectNode filteredJson = objectMapper.createObjectNode();
        
        // Get user's accessible features
        List<Feature> accessibleFeatures = featureAccessControlService.getUserAccessibleFeatures(userId);
        
        // Create a map of feature names for quick lookup
        Map<String, Feature> featureMap = accessibleFeatures.stream()
                .collect(java.util.stream.Collectors.toMap(
                        Feature::getFeatureName, 
                        feature -> feature));
        
        // Iterate through the modules JSON
        modulesJson.fields().forEachRemaining(entry -> {
            String parentModuleName = entry.getKey();
            JsonNode parentModuleNode = entry.getValue();
            
            // Check if user has access to this parent module/feature
            Feature feature = featureMap.get(parentModuleName);
            if (feature != null) {
                // Filter the modules within this feature
                ObjectNode filteredParentModule = filterModulesInFeature(userId, feature, parentModuleNode);
                if (filteredParentModule.has("modules") && 
                    filteredParentModule.get("modules").size() > 0) {
                    filteredJson.set(parentModuleName, filteredParentModule);
                }
            }
        });
        
        return filteredJson;
    }
    
    /**
     * Filter modules within a specific feature based on user's permissions
     */
    private ObjectNode filterModulesInFeature(Long userId, Feature feature, JsonNode featureNode) {
        ObjectNode filteredFeature = objectMapper.createObjectNode();
        
        // Copy feature-level properties
        featureNode.fields().forEachRemaining(entry -> {
            if (!entry.getKey().equals("modules")) {
                filteredFeature.set(entry.getKey(), entry.getValue());
            }
        });
        
        // Filter modules
        JsonNode modulesNode = featureNode.get("modules");
        if (modulesNode != null && modulesNode.isArray()) {
            ArrayNode filteredModules = objectMapper.createArrayNode();
            
            for (JsonNode moduleNode : modulesNode) {
                String moduleName = moduleNode.get("moduleName").asText();
                
                // Check if user can access this specific module
                if (featureAccessControlService.canUserAccessModule(userId, feature.getFeatureId(), moduleName)) {
                    // Filter module permissions based on user's actual permissions
                    ObjectNode filteredModule = filterModulePermissions(userId, feature.getFeatureId(), moduleNode);
                    filteredModules.add(filteredModule);
                }
            }
            
            filteredFeature.set("modules", filteredModules);
        }
        
        return filteredFeature;
    }
    
    /**
     * Filter module permissions based on user's actual permissions
     */
    private ObjectNode filterModulePermissions(Long userId, String featureId, JsonNode moduleNode) {
        ObjectNode filteredModule = objectMapper.createObjectNode();
        
        // Copy all module properties
        moduleNode.fields().forEachRemaining(entry -> {
            filteredModule.set(entry.getKey(), entry.getValue());
        });
        
        // Get user's permissions for this feature
        List<PermissionAction> userPermissions = 
                featureAccessControlService.getUserPermissionsForFeature(userId, featureId);
        
        // Filter permissions array in the module
        JsonNode permissionsNode = moduleNode.get("permissions");
        if (permissionsNode != null && permissionsNode.isArray()) {
            ArrayNode filteredPermissions = objectMapper.createArrayNode();
            
            for (JsonNode permissionNode : permissionsNode) {
                String permission = permissionNode.asText();
                
                // Map string permissions to PermissionAction enum
                try {
                    PermissionAction permissionAction = mapStringToPermissionAction(permission);
                    if (userPermissions.contains(permissionAction)) {
                        filteredPermissions.add(permissionNode);
                    }
                } catch (IllegalArgumentException e) {
                    // Unknown permission, skip it
                }
            }
            
            filteredModule.set("permissions", filteredPermissions);
        }
        
        // Filter action buttons based on permissions
        filterActionButtons(filteredModule, userPermissions);
        
        return filteredModule;
    }
    
    /**
     * Filter action buttons based on user permissions
     */
    private void filterActionButtons(ObjectNode moduleNode, List<PermissionAction> userPermissions) {
        // Filter form action buttons
        JsonNode formsNode = moduleNode.get("forms");
        if (formsNode != null && formsNode.isArray()) {
            for (JsonNode formNode : formsNode) {
                filterButtonsByPermissions((ObjectNode) formNode, "actionButtons", userPermissions);
            }
        }
        
        // Filter list action buttons
        JsonNode listsNode = moduleNode.get("lists");
        if (listsNode != null && listsNode.isArray()) {
            for (JsonNode listNode : listsNode) {
                filterButtonsByPermissions((ObjectNode) listNode, "buttons", userPermissions);
                
                // Filter table action buttons
                JsonNode tableConfigNode = listNode.get("tableConfig");
                if (tableConfigNode != null) {
                    JsonNode columnHeadersNode = tableConfigNode.get("columnHeaders");
                    if (columnHeadersNode != null && columnHeadersNode.isArray()) {
                        for (JsonNode columnNode : columnHeadersNode) {
                            if ("Actions".equals(columnNode.get("columnName").asText())) {
                                JsonNode dataSourceNode = columnNode.get("dataSource");
                                if (dataSourceNode != null && dataSourceNode.isArray()) {
                                    ArrayNode filteredActions = objectMapper.createArrayNode();
                                    for (JsonNode actionNode : dataSourceNode) {
                                        if (isActionAllowed(actionNode, userPermissions)) {
                                            filteredActions.add(actionNode);
                                        }
                                    }
                                    ((ObjectNode) columnNode).set("dataSource", filteredActions);
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    /**
     * Filter buttons by permissions
     */
    private void filterButtonsByPermissions(ObjectNode parentNode, String buttonsFieldName, 
                                          List<PermissionAction> userPermissions) {
        JsonNode buttonsNode = parentNode.get(buttonsFieldName);
        if (buttonsNode != null && buttonsNode.isArray()) {
            ArrayNode filteredButtons = objectMapper.createArrayNode();
            for (JsonNode buttonNode : buttonsNode) {
                if (isActionAllowed(buttonNode, userPermissions)) {
                    filteredButtons.add(buttonNode);
                }
            }
            parentNode.set(buttonsFieldName, filteredButtons);
        }
    }
    
    /**
     * Check if an action is allowed based on user permissions
     */
    private boolean isActionAllowed(JsonNode actionNode, List<PermissionAction> userPermissions) {
        String actionType = actionNode.get("actionType").asText();
        
        switch (actionType.toLowerCase()) {
            case "save":
            case "dialog":
                return userPermissions.contains(PermissionAction.WRITE);
            case "delete":
                return userPermissions.contains(PermissionAction.DELETE);
            case "execute":
            case "run":
                return userPermissions.contains(PermissionAction.EXECUTE);
            default:
                return userPermissions.contains(PermissionAction.READ);
        }
    }
    
    /**
     * Map string permission to PermissionAction enum
     */
    private PermissionAction mapStringToPermissionAction(String permission) {
        switch (permission.toUpperCase()) {
            case "ADMIN":
                return PermissionAction.ADMIN;
            case "READ":
                return PermissionAction.READ;
            case "WRITE":
                return PermissionAction.WRITE;
            case "DELETE":
                return PermissionAction.DELETE;
            case "EXECUTE":
                return PermissionAction.EXECUTE;
            default:
                throw new IllegalArgumentException("Unknown permission: " + permission);
        }
    }
}
