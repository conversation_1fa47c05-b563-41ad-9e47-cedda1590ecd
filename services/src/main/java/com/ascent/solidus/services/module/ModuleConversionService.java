package com.ascent.solidus.services.module;

import com.ascent.solidus.core.dao.ParentModuleRepository;
import com.ascent.solidus.core.domain.module.ModuleConfig;
import com.ascent.solidus.core.domain.module.ParentModule;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ModuleConversionService {

    @Autowired
    private ParentModuleRepository parentModuleRepo;

    public void convertParentNameToId(ModuleConfig moduleConfig) {
        if (moduleConfig.getParentName() != null) {
            ParentModule parent = parentModuleRepo.findByParentName(moduleConfig.getParentName())
                    .orElseThrow(() -> new EntityNotFoundException(
                            "Parent module not found with name: " + moduleConfig.getParentName()));
            moduleConfig.setParentModuleId(parent.getParentId());
        }
    }

    public void convertParentIdToName(ModuleConfig moduleConfig) {
        if (moduleConfig.getParentModuleId() != null) {
            ParentModule parent = parentModuleRepo.findById(moduleConfig.getParentModuleId())
                    .orElseThrow(() -> new EntityNotFoundException(
                            "Parent module not found with ID: " + moduleConfig.getParentModuleId()));
            moduleConfig.setParentName(parent.getParentName());
        }
    }
}
