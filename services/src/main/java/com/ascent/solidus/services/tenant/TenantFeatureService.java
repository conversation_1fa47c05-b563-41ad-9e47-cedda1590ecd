package com.ascent.solidus.services.tenant;

import com.ascent.solidus.core.domain.tenant.Feature;
import com.ascent.solidus.core.domain.tenant.Tenant;
import com.ascent.solidus.core.domain.tenant.TenantFeature;
import com.ascent.solidus.core.repository.tenant.TenantFeatureRepository;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service for managing tenant feature assignments
 */
@Service
public class TenantFeatureService {
    
    @Autowired
    private TenantFeatureRepository tenantFeatureRepository;
    
    @Autowired
    private TenantService tenantService;
    
    @Autowired
    private FeatureService featureService;
    
    @Transactional(readOnly = true)
    public List<Feature> getTenantFeatures(Long tenantId) {
        return tenantFeatureRepository.findValidFeaturesByTenant(tenantId);
    }
    
    @Transactional(readOnly = true)
    public List<TenantFeature> getTenantFeatureAssignments(Long tenantId) {
        Tenant tenant = tenantService.findById(tenantId);
        return tenantFeatureRepository.findByTenantAndActive(tenant, true);
    }
    
    @Transactional(readOnly = true)
    public boolean hasTenantAccessToFeature(Long tenantId, String featureId) {
        return tenantFeatureRepository.hasTenantAccessToFeature(tenantId, featureId);
    }
    
    @Transactional
    public TenantFeature assignFeatureToTenant(Long tenantId, String featureId) {
        return assignFeatureToTenant(tenantId, featureId, null);
    }
    
    @Transactional
    public TenantFeature assignFeatureToTenant(Long tenantId, String featureId, Date expiryDate) {
        Tenant tenant = tenantService.findById(tenantId);
        Feature feature = featureService.getByFeatureId(featureId);
        
        // Check if assignment already exists
        Optional<TenantFeature> existingAssignment = 
                tenantFeatureRepository.findByTenantAndFeatureAndActive(tenant, feature, true);
        
        if (existingAssignment.isPresent()) {
            TenantFeature tenantFeature = existingAssignment.get();
            tenantFeature.setEnabled(true);
            tenantFeature.setExpiryDate(expiryDate);
            return tenantFeatureRepository.save(tenantFeature);
        }
        
        TenantFeature tenantFeature = new TenantFeature(tenant, feature);
        tenantFeature.setExpiryDate(expiryDate);
        return tenantFeatureRepository.save(tenantFeature);
    }
    
    @Transactional
    public void removeFeatureFromTenant(Long tenantId, String featureId) {
        Tenant tenant = tenantService.findById(tenantId);
        Feature feature = featureService.getByFeatureId(featureId);
        
        Optional<TenantFeature> assignment = 
                tenantFeatureRepository.findByTenantAndFeatureAndActive(tenant, feature, true);
        
        if (assignment.isPresent()) {
            TenantFeature tenantFeature = assignment.get();
            tenantFeature.setActive(false);
            tenantFeatureRepository.save(tenantFeature);
        }
    }
    
    @Transactional
    public void enableFeatureForTenant(Long tenantId, String featureId) {
        updateFeatureStatus(tenantId, featureId, true);
    }
    
    @Transactional
    public void disableFeatureForTenant(Long tenantId, String featureId) {
        updateFeatureStatus(tenantId, featureId, false);
    }
    
    private void updateFeatureStatus(Long tenantId, String featureId, boolean enabled) {
        Tenant tenant = tenantService.findById(tenantId);
        Feature feature = featureService.getByFeatureId(featureId);
        
        TenantFeature tenantFeature = tenantFeatureRepository
                .findByTenantAndFeatureAndActive(tenant, feature, true)
                .orElseThrow(() -> new EntityNotFoundException(
                        "Feature assignment not found for tenant: " + tenantId + ", feature: " + featureId));
        
        tenantFeature.setEnabled(enabled);
        tenantFeatureRepository.save(tenantFeature);
    }
    
    @Transactional
    public void updateFeatureExpiry(Long tenantId, String featureId, Date expiryDate) {
        Tenant tenant = tenantService.findById(tenantId);
        Feature feature = featureService.getByFeatureId(featureId);
        
        TenantFeature tenantFeature = tenantFeatureRepository
                .findByTenantAndFeatureAndActive(tenant, feature, true)
                .orElseThrow(() -> new EntityNotFoundException(
                        "Feature assignment not found for tenant: " + tenantId + ", feature: " + featureId));
        
        tenantFeature.setExpiryDate(expiryDate);
        tenantFeatureRepository.save(tenantFeature);
    }
}
