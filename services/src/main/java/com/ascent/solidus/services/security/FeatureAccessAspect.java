package com.ascent.solidus.services.security;

import com.ascent.solidus.core.domain.module.AppUser;
import com.ascent.solidus.core.security.RequireFeatureAccess;
import com.ascent.solidus.core.security.UserPrincipal;
import com.ascent.solidus.services.tenant.FeatureAccessControlService;
import com.ascent.solidus.services.umgmt.AppUserService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

/**
 * Aspect for enforcing feature access control
 */
@Aspect
@Component
public class FeatureAccessAspect {
    
    private static final Logger logger = LoggerFactory.getLogger(FeatureAccessAspect.class);
    
    @Autowired
    private FeatureAccessControlService featureAccessControlService;
    
    @Autowired
    private AppUserService appUserService;
    
    @Around("@annotation(requireFeatureAccess)")
    public Object checkFeatureAccess(ProceedingJoinPoint joinPoint, RequireFeatureAccess requireFeatureAccess) throws Throwable {
        
        // Get current user
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
            throw new AccessDeniedException("User not authenticated");
        }
        
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        Long userId = userPrincipal.getId();
        
        String featureId = requireFeatureAccess.featureId();
        String module = requireFeatureAccess.module();
        
        // Check feature access
        boolean hasAccess;
        if (module.isEmpty()) {
            // Check general feature access with permission
            hasAccess = featureAccessControlService.hasUserPermissionForFeature(
                    userId, featureId, requireFeatureAccess.permission());
        } else {
            // Check specific module access
            hasAccess = featureAccessControlService.canUserAccessModule(userId, featureId, module) &&
                       featureAccessControlService.hasUserPermissionForFeature(
                               userId, featureId, requireFeatureAccess.permission());
        }
        
        if (!hasAccess) {
            String errorMessage = requireFeatureAccess.message();
            if (errorMessage.equals("Access denied to feature")) {
                errorMessage = String.format("Access denied to feature '%s'%s with permission '%s'", 
                                            featureId, 
                                            module.isEmpty() ? "" : " module '" + module + "'",
                                            requireFeatureAccess.permission());
            }
            
            logger.warn("Access denied for user {} to feature '{}' module '{}' with permission '{}'", 
                       userId, featureId, module, requireFeatureAccess.permission());
            
            throw new AccessDeniedException(errorMessage);
        }
        
        logger.debug("Access granted for user {} to feature '{}' module '{}' with permission '{}'", 
                    userId, featureId, module, requireFeatureAccess.permission());
        
        return joinPoint.proceed();
    }
    
    @Around("@within(requireFeatureAccess)")
    public Object checkClassFeatureAccess(ProceedingJoinPoint joinPoint, RequireFeatureAccess requireFeatureAccess) throws Throwable {
        return checkFeatureAccess(joinPoint, requireFeatureAccess);
    }
}
