package com.ascent.solidus.services.tenant;

import com.ascent.solidus.core.domain.tenant.Feature;
import com.ascent.solidus.core.repository.tenant.FeatureRepository;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service for managing features in the system
 */
@Service
public class FeatureService {
    
    @Autowired
    private FeatureRepository featureRepository;
    
    @Transactional(readOnly = true)
    public List<Feature> getAllActiveFeatures() {
        return featureRepository.findByActiveOrderByFeatureName(true);
    }
    
    @Transactional(readOnly = true)
    public Optional<Feature> findByFeatureId(String featureId) {
        return featureRepository.findByFeatureIdAndActive(featureId, true);
    }
    
    @Transactional(readOnly = true)
    public Feature getByFeatureId(String featureId) {
        return findByFeatureId(featureId)
                .orElseThrow(() -> new EntityNotFoundException("Feature not found: " + featureId));
    }
    
    @Transactional(readOnly = true)
    public Feature findById(Long id) {
        return featureRepository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("Feature not found with id: " + id));
    }
    
    @Transactional
    public Feature createFeature(String featureId, String featureName, String description) {
        // Check if feature already exists
        if (featureRepository.existsByFeatureIdAndActive(featureId, true)) {
            throw new IllegalArgumentException("Feature already exists: " + featureId);
        }
        
        Feature feature = new Feature(featureId, featureName, description);
        return featureRepository.save(feature);
    }
    
    @Transactional
    public Feature updateFeature(Long id, String featureName, String description) {
        Feature feature = findById(id);
        feature.setFeatureName(featureName);
        feature.setDescription(description);
        return featureRepository.save(feature);
    }
    
    @Transactional
    public void deactivateFeature(Long id) {
        Feature feature = findById(id);
        feature.setActive(false);
        featureRepository.save(feature);
    }
    
    @Transactional(readOnly = true)
    public List<Feature> getFeaturesByIds(List<String> featureIds) {
        return featureRepository.findByFeatureIdsAndActive(featureIds);
    }
    
    @Transactional(readOnly = true)
    public boolean featureExists(String featureId) {
        return featureRepository.existsByFeatureIdAndActive(featureId, true);
    }
}
