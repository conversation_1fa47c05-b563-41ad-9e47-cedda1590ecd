package com.ascent.solidus.services.tenant;

import com.ascent.solidus.core.constants.RoleName;
import com.ascent.solidus.core.domain.tenant.Feature;
import com.ascent.solidus.core.domain.tenant.PermissionAction;
import com.ascent.solidus.core.domain.tenant.RoleFeaturePermission;
import com.ascent.solidus.core.repository.tenant.RoleFeaturePermissionRepository;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Service for managing role-based feature permissions
 */
@Service
public class RoleFeaturePermissionService {
    
    @Autowired
    private RoleFeaturePermissionRepository roleFeaturePermissionRepository;
    
    @Autowired
    private FeatureService featureService;
    
    @Transactional(readOnly = true)
    public List<RoleFeaturePermission> getRolePermissions(RoleName roleName) {
        return roleFeaturePermissionRepository.findByRoleNameAndActive(roleName, true);
    }
    
    @Transactional(readOnly = true)
    public List<RoleFeaturePermission> getRolePermissions(List<RoleName> roleNames) {
        return roleFeaturePermissionRepository.findByRoleNamesAndActive(roleNames);
    }
    
    @Transactional(readOnly = true)
    public Optional<RoleFeaturePermission> getRoleFeaturePermission(RoleName roleName, String featureId) {
        return roleFeaturePermissionRepository.findByRoleNameAndFeatureId(roleName, featureId);
    }
    
    @Transactional(readOnly = true)
    public boolean hasRolePermissionForFeature(RoleName roleName, String featureId, PermissionAction permission) {
        return roleFeaturePermissionRepository.hasRolePermissionForFeature(roleName, featureId, permission);
    }
    
    @Transactional(readOnly = true)
    public boolean canRoleAccessModule(RoleName roleName, String featureId, String moduleName) {
        return roleFeaturePermissionRepository.canRoleAccessModule(roleName, featureId, moduleName);
    }
    
    @Transactional
    public RoleFeaturePermission createRoleFeaturePermission(RoleName roleName, String featureId, 
                                                            List<PermissionAction> permissions, 
                                                            List<String> accessibleModules) {
        Feature feature = featureService.getByFeatureId(featureId);
        
        // Check if permission already exists
        Optional<RoleFeaturePermission> existing = 
                roleFeaturePermissionRepository.findByRoleNameAndFeatureAndActive(roleName, feature, true);
        
        if (existing.isPresent()) {
            throw new IllegalArgumentException("Permission already exists for role: " + roleName + 
                                             ", feature: " + featureId);
        }
        
        RoleFeaturePermission permission = new RoleFeaturePermission(roleName, feature, permissions, accessibleModules);
        return roleFeaturePermissionRepository.save(permission);
    }
    
    @Transactional
    public RoleFeaturePermission updateRoleFeaturePermission(Long permissionId, 
                                                            List<PermissionAction> permissions, 
                                                            List<String> accessibleModules) {
        RoleFeaturePermission permission = roleFeaturePermissionRepository.findById(permissionId)
                .orElseThrow(() -> new EntityNotFoundException("Permission not found with id: " + permissionId));
        
        permission.setPermissions(permissions);
        permission.setAccessibleModules(accessibleModules);
        return roleFeaturePermissionRepository.save(permission);
    }
    
    @Transactional
    public void deleteRoleFeaturePermission(Long permissionId) {
        RoleFeaturePermission permission = roleFeaturePermissionRepository.findById(permissionId)
                .orElseThrow(() -> new EntityNotFoundException("Permission not found with id: " + permissionId));
        
        permission.setActive(false);
        roleFeaturePermissionRepository.save(permission);
    }
    
    @Transactional
    public void deleteRoleFeaturePermission(RoleName roleName, String featureId) {
        Feature feature = featureService.getByFeatureId(featureId);
        Optional<RoleFeaturePermission> permission = 
                roleFeaturePermissionRepository.findByRoleNameAndFeatureAndActive(roleName, feature, true);
        
        if (permission.isPresent()) {
            RoleFeaturePermission perm = permission.get();
            perm.setActive(false);
            roleFeaturePermissionRepository.save(perm);
        }
    }
}
