package com.ascent.solidus.services.tenant;

import com.ascent.solidus.core.constants.RoleName;
import com.ascent.solidus.core.domain.tenant.Feature;
import com.ascent.solidus.core.domain.tenant.PermissionAction;
import com.ascent.solidus.core.domain.tenant.TenantFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Service for loading and managing feature configurations from JSON files
 */
@Service
public class FeatureConfigurationService {
    
    private static final Logger logger = LoggerFactory.getLogger(FeatureConfigurationService.class);
    
    @Autowired
    private FeatureService featureService;
    
    @Autowired
    private TenantFeatureService tenantFeatureService;
    
    @Autowired
    private RoleFeaturePermissionService roleFeaturePermissionService;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
    
    /**
     * Load features from JSON configuration
     */
    @Transactional
    public void loadFeaturesFromConfig() {
        try {
            ClassPathResource resource = new ClassPathResource("json/tenant-features.json");
            JsonNode rootNode = objectMapper.readTree(resource.getInputStream());
            JsonNode featureDefinitions = rootNode.get("feature_definitions");
            
            if (featureDefinitions != null) {
                featureDefinitions.fields().forEachRemaining(entry -> {
                    String featureId = entry.getKey();
                    JsonNode featureNode = entry.getValue();
                    
                    String featureName = featureNode.get("feature_name").asText();
                    String description = featureNode.get("description").asText();
                    
                    try {
                        if (!featureService.featureExists(featureId)) {
                            featureService.createFeature(featureId, featureName, description);
                            logger.info("Created feature: {} - {}", featureId, featureName);
                        }
                    } catch (Exception e) {
                        logger.error("Error creating feature {}: {}", featureId, e.getMessage());
                    }
                });
            }
        } catch (IOException e) {
            logger.error("Error loading features from configuration: {}", e.getMessage());
        }
    }
    
    /**
     * Load tenant feature assignments from JSON configuration
     */
    @Transactional
    public void loadTenantFeaturesFromConfig() {
        try {
            ClassPathResource resource = new ClassPathResource("json/tenant-features.json");
            JsonNode rootNode = objectMapper.readTree(resource.getInputStream());
            JsonNode tenantFeatures = rootNode.get("tenant_features");
            
            if (tenantFeatures != null) {
                tenantFeatures.fields().forEachRemaining(tenantEntry -> {
                    JsonNode tenantNode = tenantEntry.getValue();
                    Long tenantId = tenantNode.get("tenant_id").asLong();
                    
                    JsonNode assignedFeatures = tenantNode.get("assigned_features");
                    if (assignedFeatures != null && assignedFeatures.isArray()) {
                        for (JsonNode featureNode : assignedFeatures) {
                            String featureId = featureNode.get("feature_id").asText();
                            boolean enabled = featureNode.get("enabled").asBoolean();
                            
                            Date expiryDate = null;
                            if (featureNode.has("expiry_date") && !featureNode.get("expiry_date").isNull()) {
                                try {
                                    expiryDate = dateFormat.parse(featureNode.get("expiry_date").asText());
                                } catch (ParseException e) {
                                    logger.warn("Invalid expiry date format for feature {}: {}", 
                                              featureId, featureNode.get("expiry_date").asText());
                                }
                            }
                            
                            try {
                                if (enabled && !tenantFeatureService.hasTenantAccessToFeature(tenantId, featureId)) {
                                    tenantFeatureService.assignFeatureToTenant(tenantId, featureId, expiryDate);
                                    logger.info("Assigned feature {} to tenant {}", featureId, tenantId);
                                }
                            } catch (Exception e) {
                                logger.error("Error assigning feature {} to tenant {}: {}", 
                                           featureId, tenantId, e.getMessage());
                            }
                        }
                    }
                });
            }
        } catch (IOException e) {
            logger.error("Error loading tenant features from configuration: {}", e.getMessage());
        }
    }
    
    /**
     * Load role feature permissions from JSON configuration
     */
    @Transactional
    public void loadRolePermissionsFromConfig() {
        try {
            ClassPathResource resource = new ClassPathResource("json/role-feature-permissions.json");
            JsonNode rootNode = objectMapper.readTree(resource.getInputStream());
            JsonNode rolePermissions = rootNode.get("role_feature_permissions");
            
            if (rolePermissions != null) {
                rolePermissions.fields().forEachRemaining(roleEntry -> {
                    String roleNameStr = roleEntry.getKey();
                    RoleName roleName;
                    
                    try {
                        roleName = RoleName.valueOf(roleNameStr);
                    } catch (IllegalArgumentException e) {
                        logger.warn("Invalid role name: {}", roleNameStr);
                        return;
                    }
                    
                    JsonNode roleNode = roleEntry.getValue();
                    JsonNode accessibleFeatures = roleNode.get("accessible_features");
                    
                    if (accessibleFeatures != null && accessibleFeatures.isArray()) {
                        for (JsonNode featureNode : accessibleFeatures) {
                            String featureId = featureNode.get("feature_id").asText();
                            
                            List<PermissionAction> permissions = new ArrayList<>();
                            JsonNode permissionsNode = featureNode.get("permissions");
                            if (permissionsNode != null && permissionsNode.isArray()) {
                                for (JsonNode permNode : permissionsNode) {
                                    try {
                                        permissions.add(PermissionAction.valueOf(permNode.asText()));
                                    } catch (IllegalArgumentException e) {
                                        logger.warn("Invalid permission action: {}", permNode.asText());
                                    }
                                }
                            }
                            
                            List<String> modules = new ArrayList<>();
                            JsonNode modulesNode = featureNode.get("modules");
                            if (modulesNode != null && modulesNode.isArray()) {
                                for (JsonNode moduleNode : modulesNode) {
                                    modules.add(moduleNode.asText());
                                }
                            }
                            
                            try {
                                if (roleFeaturePermissionService.getRoleFeaturePermission(roleName, featureId).isEmpty()) {
                                    roleFeaturePermissionService.createRoleFeaturePermission(
                                            roleName, featureId, permissions, modules);
                                    logger.info("Created permission for role {} on feature {}", roleName, featureId);
                                }
                            } catch (Exception e) {
                                logger.error("Error creating permission for role {} on feature {}: {}", 
                                           roleName, featureId, e.getMessage());
                            }
                        }
                    }
                });
            }
        } catch (IOException e) {
            logger.error("Error loading role permissions from configuration: {}", e.getMessage());
        }
    }
    
    /**
     * Initialize all configurations
     */
    @Transactional
    public void initializeConfigurations() {
        logger.info("Initializing feature configurations...");
        loadFeaturesFromConfig();
        loadTenantFeaturesFromConfig();
        loadRolePermissionsFromConfig();
        logger.info("Feature configurations initialized successfully");
    }
}
