package com.ascent.solidus.services.module;

import com.ascent.solidus.core.dao.ModuleRepository;
import com.ascent.solidus.core.domain.module.FieldConfig;
import com.ascent.solidus.core.domain.module.ModuleConfig;
import jakarta.persistence.EntityNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FieldService {

    @Autowired
    private ModuleRepository moduleRepository;

    @Autowired
    private FieldRepository fieldRepository;

    public FieldConfig addFieldToModule(String moduleId, FieldConfig fieldConfig) {
        ModuleConfig module = moduleRepository.findById(moduleId)
                .orElseThrow(() -> new EntityNotFoundException("Module not found"));

        fieldConfig.setModuleId(moduleId);
        FieldConfig saved = fieldRepository.save(fieldConfig);

        // Add to module's field list if not already present
        if (module.getFields() == null) {
            module.setFields(new ArrayList<>());
        }
        module.getFields().add(saved);
        moduleRepository.save(module);

        return saved;
    }

    public List<FieldConfig> getFieldsByModuleId(String moduleId) {
        return fieldRepository.findByModuleId(moduleId);
    }

    public FieldConfig updateField(String moduleId, String fieldName, FieldConfig fieldConfig) {
        FieldConfig existing = fieldRepository.findByModuleIdAndFieldName(moduleId, fieldName)
                .orElseThrow(() -> new EntityNotFoundException("Field not found"));

        // Update fields
        existing.setFieldType(fieldConfig.getFieldType());
        existing.setRequired(fieldConfig.isRequired());
        // Update other properties

        return fieldRepository.save(existing);
    }

    public void deleteField(String moduleId, String fieldName) {
        FieldConfig field = fieldRepository.findByModuleIdAndFieldName(moduleId, fieldName)
                .orElseThrow(() -> new EntityNotFoundException("Field not found"));

        // Remove from module's field list
        ModuleConfig module = moduleRepository.findById(moduleId)
                .orElseThrow(() -> new EntityNotFoundException("Module not found"));

        if (module.getFields() != null) {
            module.getFields().removeIf(f -> f.getFieldName().equals(fieldName));
            moduleRepository.save(module);
        }

        fieldRepository.delete(field);
    }
}
