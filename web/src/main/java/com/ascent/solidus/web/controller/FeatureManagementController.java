package com.ascent.solidus.web.controller;

import com.ascent.solidus.core.constants.RoleName;
import com.ascent.solidus.core.domain.tenant.Feature;
import com.ascent.solidus.core.domain.tenant.PermissionAction;
import com.ascent.solidus.core.domain.tenant.RoleFeaturePermission;
import com.ascent.solidus.core.domain.tenant.TenantFeature;
import com.ascent.solidus.core.security.RequireFeatureAccess;
import com.ascent.solidus.services.tenant.FeatureAccessControlService;
import com.ascent.solidus.services.tenant.FeatureConfigurationService;
import com.ascent.solidus.services.tenant.FeatureService;
import com.ascent.solidus.services.tenant.RoleFeaturePermissionService;
import com.ascent.solidus.services.tenant.TenantFeatureService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * REST Controller for managing features, tenant assignments, and role permissions
 */
@RestController
@RequestMapping("/api/features")
@PreAuthorize("hasRole('SUPER_ADMIN') or hasRole('ORGANIZATION_ADMIN')")
public class FeatureManagementController {
    
    @Autowired
    private FeatureService featureService;
    
    @Autowired
    private TenantFeatureService tenantFeatureService;
    
    @Autowired
    private RoleFeaturePermissionService roleFeaturePermissionService;
    
    @Autowired
    private FeatureAccessControlService featureAccessControlService;
    
    @Autowired
    private FeatureConfigurationService featureConfigurationService;
    
    // Feature Management APIs
    
    @GetMapping
    public ResponseEntity<List<Feature>> getAllFeatures() {
        List<Feature> features = featureService.getAllActiveFeatures();
        return ResponseEntity.ok(features);
    }
    
    @GetMapping("/{featureId}")
    public ResponseEntity<Feature> getFeature(@PathVariable String featureId) {
        return featureService.findByFeatureId(featureId)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @PostMapping
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<Feature> createFeature(@RequestBody CreateFeatureRequest request) {
        Feature feature = featureService.createFeature(
                request.getFeatureId(), 
                request.getFeatureName(), 
                request.getDescription());
        return ResponseEntity.ok(feature);
    }
    
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<Feature> updateFeature(@PathVariable Long id, 
                                               @RequestBody UpdateFeatureRequest request) {
        Feature feature = featureService.updateFeature(id, request.getFeatureName(), request.getDescription());
        return ResponseEntity.ok(feature);
    }
    
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<Void> deleteFeature(@PathVariable Long id) {
        featureService.deactivateFeature(id);
        return ResponseEntity.ok().build();
    }
    
    // Tenant Feature Assignment APIs
    
    @GetMapping("/tenant/{tenantId}")
    public ResponseEntity<List<Feature>> getTenantFeatures(@PathVariable Long tenantId) {
        List<Feature> features = tenantFeatureService.getTenantFeatures(tenantId);
        return ResponseEntity.ok(features);
    }
    
    @GetMapping("/tenant/{tenantId}/assignments")
    public ResponseEntity<List<TenantFeature>> getTenantFeatureAssignments(@PathVariable Long tenantId) {
        List<TenantFeature> assignments = tenantFeatureService.getTenantFeatureAssignments(tenantId);
        return ResponseEntity.ok(assignments);
    }
    
    @PostMapping("/tenant/{tenantId}/assign/{featureId}")
    public ResponseEntity<TenantFeature> assignFeatureToTenant(@PathVariable Long tenantId, 
                                                             @PathVariable String featureId,
                                                             @RequestBody(required = false) AssignFeatureRequest request) {
        Date expiryDate = request != null ? request.getExpiryDate() : null;
        TenantFeature assignment = tenantFeatureService.assignFeatureToTenant(tenantId, featureId, expiryDate);
        return ResponseEntity.ok(assignment);
    }
    
    @DeleteMapping("/tenant/{tenantId}/remove/{featureId}")
    public ResponseEntity<Void> removeFeatureFromTenant(@PathVariable Long tenantId, 
                                                       @PathVariable String featureId) {
        tenantFeatureService.removeFeatureFromTenant(tenantId, featureId);
        return ResponseEntity.ok().build();
    }
    
    @PutMapping("/tenant/{tenantId}/enable/{featureId}")
    public ResponseEntity<Void> enableFeatureForTenant(@PathVariable Long tenantId, 
                                                      @PathVariable String featureId) {
        tenantFeatureService.enableFeatureForTenant(tenantId, featureId);
        return ResponseEntity.ok().build();
    }
    
    @PutMapping("/tenant/{tenantId}/disable/{featureId}")
    public ResponseEntity<Void> disableFeatureForTenant(@PathVariable Long tenantId, 
                                                       @PathVariable String featureId) {
        tenantFeatureService.disableFeatureForTenant(tenantId, featureId);
        return ResponseEntity.ok().build();
    }
    
    // Role Permission APIs
    
    @GetMapping("/roles/{roleName}/permissions")
    public ResponseEntity<List<RoleFeaturePermission>> getRolePermissions(@PathVariable RoleName roleName) {
        List<RoleFeaturePermission> permissions = roleFeaturePermissionService.getRolePermissions(roleName);
        return ResponseEntity.ok(permissions);
    }
    
    @PostMapping("/roles/{roleName}/permissions")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<RoleFeaturePermission> createRolePermission(@PathVariable RoleName roleName,
                                                                     @RequestBody CreateRolePermissionRequest request) {
        RoleFeaturePermission permission = roleFeaturePermissionService.createRoleFeaturePermission(
                roleName, 
                request.getFeatureId(), 
                request.getPermissions(), 
                request.getAccessibleModules());
        return ResponseEntity.ok(permission);
    }
    
    @PutMapping("/permissions/{permissionId}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<RoleFeaturePermission> updateRolePermission(@PathVariable Long permissionId,
                                                                     @RequestBody UpdateRolePermissionRequest request) {
        RoleFeaturePermission permission = roleFeaturePermissionService.updateRoleFeaturePermission(
                permissionId, 
                request.getPermissions(), 
                request.getAccessibleModules());
        return ResponseEntity.ok(permission);
    }
    
    @DeleteMapping("/permissions/{permissionId}")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<Void> deleteRolePermission(@PathVariable Long permissionId) {
        roleFeaturePermissionService.deleteRoleFeaturePermission(permissionId);
        return ResponseEntity.ok().build();
    }
    
    // Access Control APIs
    
    @GetMapping("/user/{userId}/accessible")
    public ResponseEntity<List<Feature>> getUserAccessibleFeatures(@PathVariable Long userId) {
        List<Feature> features = featureAccessControlService.getUserAccessibleFeatures(userId);
        return ResponseEntity.ok(features);
    }
    
    @GetMapping("/user/{userId}/check/{featureId}")
    public ResponseEntity<Map<String, Object>> checkUserFeatureAccess(@PathVariable Long userId, 
                                                                     @PathVariable String featureId,
                                                                     @RequestParam(required = false) String module,
                                                                     @RequestParam(required = false) PermissionAction permission) {
        boolean hasAccess;
        if (module != null && !module.isEmpty()) {
            hasAccess = featureAccessControlService.canUserAccessModule(userId, featureId, module);
        } else if (permission != null) {
            hasAccess = featureAccessControlService.hasUserPermissionForFeature(userId, featureId, permission);
        } else {
            hasAccess = featureAccessControlService.hasUserAccessToFeature(userId, featureId);
        }
        
        return ResponseEntity.ok(Map.of(
                "hasAccess", hasAccess,
                "userId", userId,
                "featureId", featureId,
                "module", module != null ? module : "",
                "permission", permission != null ? permission.toString() : ""
        ));
    }
    
    // Configuration Management APIs
    
    @PostMapping("/config/reload")
    @PreAuthorize("hasRole('SUPER_ADMIN')")
    public ResponseEntity<Map<String, String>> reloadConfigurations() {
        featureConfigurationService.initializeConfigurations();
        return ResponseEntity.ok(Map.of("message", "Configurations reloaded successfully"));
    }
    
    // Request/Response DTOs
    
    public static class CreateFeatureRequest {
        private String featureId;
        private String featureName;
        private String description;
        
        // Getters and setters
        public String getFeatureId() { return featureId; }
        public void setFeatureId(String featureId) { this.featureId = featureId; }
        public String getFeatureName() { return featureName; }
        public void setFeatureName(String featureName) { this.featureName = featureName; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }
    
    public static class UpdateFeatureRequest {
        private String featureName;
        private String description;
        
        // Getters and setters
        public String getFeatureName() { return featureName; }
        public void setFeatureName(String featureName) { this.featureName = featureName; }
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }
    
    public static class AssignFeatureRequest {
        private Date expiryDate;
        
        // Getters and setters
        public Date getExpiryDate() { return expiryDate; }
        public void setExpiryDate(Date expiryDate) { this.expiryDate = expiryDate; }
    }
    
    public static class CreateRolePermissionRequest {
        private String featureId;
        private List<PermissionAction> permissions;
        private List<String> accessibleModules;
        
        // Getters and setters
        public String getFeatureId() { return featureId; }
        public void setFeatureId(String featureId) { this.featureId = featureId; }
        public List<PermissionAction> getPermissions() { return permissions; }
        public void setPermissions(List<PermissionAction> permissions) { this.permissions = permissions; }
        public List<String> getAccessibleModules() { return accessibleModules; }
        public void setAccessibleModules(List<String> accessibleModules) { this.accessibleModules = accessibleModules; }
    }
    
    public static class UpdateRolePermissionRequest {
        private List<PermissionAction> permissions;
        private List<String> accessibleModules;
        
        // Getters and setters
        public List<PermissionAction> getPermissions() { return permissions; }
        public void setPermissions(List<PermissionAction> permissions) { this.permissions = permissions; }
        public List<String> getAccessibleModules() { return accessibleModules; }
        public void setAccessibleModules(List<String> accessibleModules) { this.accessibleModules = accessibleModules; }
    }
}
