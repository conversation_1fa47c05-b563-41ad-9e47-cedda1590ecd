package com.ascent.solidus.web.controller;

import com.ascent.solidus.core.security.UserPrincipal;
import com.ascent.solidus.services.security.ModuleFilterService;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

/**
 * Controller for providing user-specific filtered modules
 */
@RestController
@RequestMapping("/api/user/modules")
public class UserModuleController {
    
    @Autowired
    private ModuleFilterService moduleFilterService;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * Get modules filtered by current user's permissions
     */
    @GetMapping
    public ResponseEntity<JsonNode> getUserModules() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
            return ResponseEntity.unauthorized().build();
        }
        
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        Long userId = userPrincipal.getId();
        
        try {
            // Load the endUserModules.json file
            ClassPathResource resource = new ClassPathResource("json/endUserModules.json");
            JsonNode modulesJson = objectMapper.readTree(resource.getInputStream());
            
            // Filter modules based on user's permissions
            JsonNode filteredModules = moduleFilterService.filterModulesForUser(userId, modulesJson);
            
            return ResponseEntity.ok(filteredModules);
            
        } catch (IOException e) {
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Get specific feature modules filtered by current user's permissions
     */
    @GetMapping("/{featureName}")
    public ResponseEntity<JsonNode> getUserFeatureModules(@PathVariable String featureName) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
            return ResponseEntity.unauthorized().build();
        }
        
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        Long userId = userPrincipal.getId();
        
        try {
            // Load the endUserModules.json file
            ClassPathResource resource = new ClassPathResource("json/endUserModules.json");
            JsonNode modulesJson = objectMapper.readTree(resource.getInputStream());
            
            // Get the specific feature
            JsonNode featureNode = modulesJson.get(featureName);
            if (featureNode == null) {
                return ResponseEntity.notFound().build();
            }
            
            // Create a temporary JSON with just this feature
            JsonNode tempJson = objectMapper.createObjectNode().set(featureName, featureNode);
            
            // Filter modules based on user's permissions
            JsonNode filteredModules = moduleFilterService.filterModulesForUser(userId, tempJson);
            
            // Return just the filtered feature
            JsonNode filteredFeature = filteredModules.get(featureName);
            if (filteredFeature == null) {
                return ResponseEntity.notFound().build();
            }
            
            return ResponseEntity.ok(filteredFeature);
            
        } catch (IOException e) {
            return ResponseEntity.internalServerError().build();
        }
    }
    
    /**
     * Get modules from a specific JSON file filtered by current user's permissions
     */
    @GetMapping("/file/{fileName}")
    public ResponseEntity<JsonNode> getUserModulesFromFile(@PathVariable String fileName) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !(authentication.getPrincipal() instanceof UserPrincipal)) {
            return ResponseEntity.unauthorized().build();
        }
        
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        Long userId = userPrincipal.getId();
        
        try {
            // Validate file name to prevent path traversal
            if (!fileName.matches("^[a-zA-Z0-9_-]+\\.json$")) {
                return ResponseEntity.badRequest().build();
            }
            
            // Load the specified JSON file
            ClassPathResource resource = new ClassPathResource("json/" + fileName);
            if (!resource.exists()) {
                return ResponseEntity.notFound().build();
            }
            
            JsonNode modulesJson = objectMapper.readTree(resource.getInputStream());
            
            // Filter modules based on user's permissions
            JsonNode filteredModules = moduleFilterService.filterModulesForUser(userId, modulesJson);
            
            return ResponseEntity.ok(filteredModules);
            
        } catch (IOException e) {
            return ResponseEntity.internalServerError().build();
        }
    }
}
