package com.ascent.solidus.core.repository.tenant;

import com.ascent.solidus.core.domain.tenant.Feature;
import com.ascent.solidus.core.domain.tenant.Tenant;
import com.ascent.solidus.core.domain.tenant.TenantFeature;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TenantFeatureRepository extends JpaRepository<TenantFeature, Long> {
    
    List<TenantFeature> findByTenantAndActive(Tenant tenant, boolean active);
    
    List<TenantFeature> findByTenantAndEnabledAndActive(Tenant tenant, boolean enabled, boolean active);
    
    Optional<TenantFeature> findByTenantAndFeatureAndActive(Tenant tenant, Feature feature, boolean active);
    
    @Query("SELECT tf FROM TenantFeature tf WHERE tf.tenant.id = :tenantId AND tf.enabled = true AND tf.active = true " +
           "AND (tf.expiryDate IS NULL OR tf.expiryDate > CURRENT_TIMESTAMP)")
    List<TenantFeature> findValidFeaturesByTenantId(@Param("tenantId") Long tenantId);
    
    @Query("SELECT tf.feature FROM TenantFeature tf WHERE tf.tenant.id = :tenantId AND tf.enabled = true AND tf.active = true " +
           "AND (tf.expiryDate IS NULL OR tf.expiryDate > CURRENT_TIMESTAMP)")
    List<Feature> findValidFeaturesByTenant(@Param("tenantId") Long tenantId);
    
    @Query("SELECT CASE WHEN COUNT(tf) > 0 THEN true ELSE false END FROM TenantFeature tf " +
           "WHERE tf.tenant.id = :tenantId AND tf.feature.featureId = :featureId AND tf.enabled = true AND tf.active = true " +
           "AND (tf.expiryDate IS NULL OR tf.expiryDate > CURRENT_TIMESTAMP)")
    boolean hasTenantAccessToFeature(@Param("tenantId") Long tenantId, @Param("featureId") String featureId);
}
