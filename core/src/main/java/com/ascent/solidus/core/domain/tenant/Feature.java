package com.ascent.solidus.core.domain.tenant;

import com.ascent.solidus.core.domain.AbstractEntity;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.util.HashSet;
import java.util.Set;

/**
 * Entity representing a feature that can be assigned to tenants
 * Features are like Product Management, Audits, Calibration, etc.
 */
@Entity
@Table(name = "features")
public class Feature extends AbstractEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Size(max = 100)
    @Column(nullable = false, unique = true)
    private String featureId; // e.g., "product_management", "audits"
    
    @NotBlank
    @Size(max = 200)
    @Column(nullable = false)
    private String featureName; // e.g., "Product Management", "Audits"
    
    @Size(max = 500)
    private String description;
    
    @Column(nullable = false)
    private boolean active = true;
    
    @OneToMany(mappedBy = "feature", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<TenantFeature> tenantFeatures = new HashSet<>();
    
    @OneToMany(mappedBy = "feature", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<RoleFeaturePermission> rolePermissions = new HashSet<>();
    
    // Constructors
    public Feature() {}
    
    public Feature(String featureId, String featureName, String description) {
        this.featureId = featureId;
        this.featureName = featureName;
        this.description = description;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getFeatureId() {
        return featureId;
    }
    
    public void setFeatureId(String featureId) {
        this.featureId = featureId;
    }
    
    public String getFeatureName() {
        return featureName;
    }
    
    public void setFeatureName(String featureName) {
        this.featureName = featureName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public boolean isActive() {
        return active;
    }
    
    public void setActive(boolean active) {
        this.active = active;
    }
    
    public Set<TenantFeature> getTenantFeatures() {
        return tenantFeatures;
    }
    
    public void setTenantFeatures(Set<TenantFeature> tenantFeatures) {
        this.tenantFeatures = tenantFeatures;
    }
    
    public Set<RoleFeaturePermission> getRolePermissions() {
        return rolePermissions;
    }
    
    public void setRolePermissions(Set<RoleFeaturePermission> rolePermissions) {
        this.rolePermissions = rolePermissions;
    }
}
