package com.ascent.solidus.core.domain.tenant;

import com.ascent.solidus.core.constants.RoleName;
import com.ascent.solidus.core.domain.AbstractEntity;
import jakarta.persistence.*;

import java.util.List;

/**
 * Entity representing permissions for roles to access specific features
 * This defines what roles can access which features and with what permissions
 */
@Entity
@Table(name = "role_feature_permissions", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"role_name", "feature_id"})
})
public class RoleFeaturePermission extends AbstractEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "role_name", nullable = false)
    private RoleName roleName;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "feature_id", nullable = false)
    private Feature feature;
    
    @ElementCollection(fetch = FetchType.EAGER)
    @Enumerated(EnumType.STRING)
    @CollectionTable(name = "role_feature_permissions_actions", 
                     joinColumns = @JoinColumn(name = "permission_id"))
    @Column(name = "permission_action")
    private List<PermissionAction> permissions;
    
    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(name = "role_feature_accessible_modules", 
                     joinColumns = @JoinColumn(name = "permission_id"))
    @Column(name = "module_name")
    private List<String> accessibleModules; // e.g., ["Products", "Revisions", "Assemblies"]
    
    @Column(nullable = false)
    private boolean active = true;
    
    // Constructors
    public RoleFeaturePermission() {}
    
    public RoleFeaturePermission(RoleName roleName, Feature feature, 
                                List<PermissionAction> permissions, 
                                List<String> accessibleModules) {
        this.roleName = roleName;
        this.feature = feature;
        this.permissions = permissions;
        this.accessibleModules = accessibleModules;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public RoleName getRoleName() {
        return roleName;
    }
    
    public void setRoleName(RoleName roleName) {
        this.roleName = roleName;
    }
    
    public Feature getFeature() {
        return feature;
    }
    
    public void setFeature(Feature feature) {
        this.feature = feature;
    }
    
    public List<PermissionAction> getPermissions() {
        return permissions;
    }
    
    public void setPermissions(List<PermissionAction> permissions) {
        this.permissions = permissions;
    }
    
    public List<String> getAccessibleModules() {
        return accessibleModules;
    }
    
    public void setAccessibleModules(List<String> accessibleModules) {
        this.accessibleModules = accessibleModules;
    }
    
    public boolean isActive() {
        return active;
    }
    
    public void setActive(boolean active) {
        this.active = active;
    }
    
    /**
     * Check if this role has a specific permission for the feature
     */
    public boolean hasPermission(PermissionAction action) {
        return permissions != null && permissions.contains(action);
    }
    
    /**
     * Check if this role can access a specific module within the feature
     */
    public boolean canAccessModule(String moduleName) {
        return accessibleModules == null || accessibleModules.isEmpty() || 
               accessibleModules.contains(moduleName);
    }
}
