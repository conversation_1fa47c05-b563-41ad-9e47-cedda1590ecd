package com.ascent.solidus.core.repository.tenant;

import com.ascent.solidus.core.domain.tenant.Feature;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FeatureRepository extends JpaRepository<Feature, Long> {
    
    Optional<Feature> findByFeatureIdAndActive(String featureId, boolean active);
    
    List<Feature> findByActiveOrderByFeatureName(boolean active);
    
    @Query("SELECT f FROM Feature f WHERE f.featureId IN :featureIds AND f.active = true")
    List<Feature> findByFeatureIdsAndActive(@Param("featureIds") List<String> featureIds);
    
    boolean existsByFeatureIdAndActive(String featureId, boolean active);
}
