package com.ascent.solidus.core.domain.tenant;

import com.ascent.solidus.core.domain.AbstractEntity;
import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;

import java.util.Date;

/**
 * Entity representing the assignment of a feature to a tenant
 * This tracks which features are enabled for which tenants
 */
@Entity
@Table(name = "tenant_features", uniqueConstraints = {
    @UniqueConstraint(columnNames = {"tenant_id", "feature_id"})
})
public class TenantFeature extends AbstractEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "tenant_id", nullable = false)
    private Tenant tenant;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "feature_id", nullable = false)
    private Feature feature;
    
    @Column(nullable = false)
    private boolean enabled = true;
    
    @CreationTimestamp
    @Temporal(TemporalType.TIMESTAMP)
    @Column(nullable = false)
    private Date assignedDate;
    
    @Temporal(TemporalType.TIMESTAMP)
    private Date expiryDate; // Optional: for time-limited features
    
    @Column(nullable = false)
    private boolean active = true;
    
    // Constructors
    public TenantFeature() {}
    
    public TenantFeature(Tenant tenant, Feature feature) {
        this.tenant = tenant;
        this.feature = feature;
        this.enabled = true;
        this.assignedDate = new Date();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Tenant getTenant() {
        return tenant;
    }
    
    public void setTenant(Tenant tenant) {
        this.tenant = tenant;
    }
    
    public Feature getFeature() {
        return feature;
    }
    
    public void setFeature(Feature feature) {
        this.feature = feature;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public Date getAssignedDate() {
        return assignedDate;
    }
    
    public void setAssignedDate(Date assignedDate) {
        this.assignedDate = assignedDate;
    }
    
    public Date getExpiryDate() {
        return expiryDate;
    }
    
    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }
    
    public boolean isActive() {
        return active;
    }
    
    public void setActive(boolean active) {
        this.active = active;
    }
    
    /**
     * Check if this feature assignment is currently valid
     */
    public boolean isValid() {
        return active && enabled && 
               (expiryDate == null || expiryDate.after(new Date()));
    }
}
