package com.ascent.solidus.core.security;

import com.ascent.solidus.core.domain.tenant.PermissionAction;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Annotation to require feature access for method execution
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireFeatureAccess {
    
    /**
     * The feature ID that is required
     */
    String featureId();
    
    /**
     * The permission action required (default is READ)
     */
    PermissionAction permission() default PermissionAction.READ;
    
    /**
     * The specific module within the feature (optional)
     */
    String module() default "";
    
    /**
     * Error message to show when access is denied
     */
    String message() default "Access denied to feature";
}
