package com.ascent.solidus.core.repository.tenant;

import com.ascent.solidus.core.constants.RoleName;
import com.ascent.solidus.core.domain.tenant.Feature;
import com.ascent.solidus.core.domain.tenant.PermissionAction;
import com.ascent.solidus.core.domain.tenant.RoleFeaturePermission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RoleFeaturePermissionRepository extends JpaRepository<RoleFeaturePermission, Long> {
    
    List<RoleFeaturePermission> findByRoleNameAndActive(RoleName roleName, boolean active);
    
    Optional<RoleFeaturePermission> findByRoleNameAndFeatureAndActive(RoleName roleName, Feature feature, boolean active);
    
    @Query("SELECT rfp FROM RoleFeaturePermission rfp WHERE rfp.roleName = :roleName " +
           "AND rfp.feature.featureId = :featureId AND rfp.active = true")
    Optional<RoleFeaturePermission> findByRoleNameAndFeatureId(@Param("roleName") RoleName roleName, 
                                                               @Param("featureId") String featureId);
    
    @Query("SELECT rfp FROM RoleFeaturePermission rfp WHERE rfp.roleName IN :roleNames AND rfp.active = true")
    List<RoleFeaturePermission> findByRoleNamesAndActive(@Param("roleNames") List<RoleName> roleNames);
    
    @Query("SELECT CASE WHEN COUNT(rfp) > 0 THEN true ELSE false END FROM RoleFeaturePermission rfp " +
           "WHERE rfp.roleName = :roleName AND rfp.feature.featureId = :featureId AND rfp.active = true " +
           "AND :permission MEMBER OF rfp.permissions")
    boolean hasRolePermissionForFeature(@Param("roleName") RoleName roleName, 
                                       @Param("featureId") String featureId, 
                                       @Param("permission") PermissionAction permission);
    
    @Query("SELECT CASE WHEN COUNT(rfp) > 0 THEN true ELSE false END FROM RoleFeaturePermission rfp " +
           "WHERE rfp.roleName = :roleName AND rfp.feature.featureId = :featureId AND rfp.active = true " +
           "AND (:moduleName MEMBER OF rfp.accessibleModules OR rfp.accessibleModules IS EMPTY)")
    boolean canRoleAccessModule(@Param("roleName") RoleName roleName, 
                               @Param("featureId") String featureId, 
                               @Param("moduleName") String moduleName);
}
