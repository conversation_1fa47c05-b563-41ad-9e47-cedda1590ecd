{"documentInfo": {"companyName": "VIKRAM SOLAR LIMITED", "docNo": "VSL/FAB3/QAD/FM/49", "issueDate": "23.09.2024", "title": "INPROCESS QUALITY AUDIT REPORT LINE 1", "revNo": "01", "revDate": "21.10.2024"}, "headerInfo": {"dateShift": "", "productionOrderNo": "", "moduleType": "PARADEA/SOMERA/PREXOS/HYPERSOL", "customerSpecAvailable": "", "specSignOff": ""}, "auditRows": [{"id": "row-1", "sNo": 1, "stages": [{"id": "stage-1-1", "stage": "Pre Lam Shop Floor Condition", "inspectionTypes": [{"id": "inspection-1-1-1", "typeOfInspection": "Aesthetics", "parameters": [{"id": "param-1-1-1-1", "parameter": "<PERSON><PERSON><PERSON><PERSON>", "criteria": [{"id": "criteria-1-1-1-1-1", "criteria": "<60%", "inspectionFrequency": "Every shift", "observations": {"type": "simpleGrid", "description": "Basic grid layout with time-based columns for simple data entry", "column_nos": 4, "row_nos": 1, "columns_labels": ["2 hrs", "4 hrs", "6 hrs", "8 hrs"], "values": {"grid_data": [["344", "", "", ""]]}}}]}]}]}]}, {"id": "row-2", "sNo": 2, "stages": [{"id": "stage-2-1", "stage": "Auto Front Glass loading", "inspectionTypes": [{"id": "inspection-2-1-1", "typeOfInspection": "Aesthetics", "parameters": [{"id": "param-2-1-1-1", "parameter": "Front glass Status", "criteria": [{"id": "criteria-2-1-1-1-1", "criteria": "Maker, Lot NO and Expiry date", "inspectionFrequency": "Every shift", "observations": {"type": "multiColumnTable", "description": "Multiple side-by-side tables with labeled fields for structured data entry", "column_nos": 3, "column_border_within_table": true, "table_labels": ["Stringer - 1", "Stringer - 2", "Stringer - 3"], "columns": ["Unit - \"A\" Groove Laser Head Power", "Unit - \"A\" Groove Laser Tail Power", "Unit - \"A\" Groove Laser Tail Power"], "rows": [[{"label": "Maker", "unit": "mm", "readonly": false}, {"label": "Efficiency", "unit": null, "readonly": false}, {"label": "Exp", "unit": "mm", "readonly": false}], [{"label": "Maker", "unit": "mm", "readonly": false}, {"label": "Efficiency", "unit": null, "readonly": false}, {"label": "Exp", "unit": null, "readonly": false}]], "values": {"table_data": [[["", "", ""], ["", "", ""]], [["", "", ""], ["", "", ""]], [["", "", ""], ["", "", ""]]]}}}]}]}]}]}, {"sNo": 4, "stages": [{"stage": "Cell sorting", "inspectionTypes": [{"typeOfInspection": "Aesthetics", "parameters": [{"parameter": "Cell status", "criteria": [{"id": "4-1-1-1", "criteria": "As per Order", "inspectionFrequency": "Every 4 hours", "observations": {"type": "hierarchicalTable", "description": "Complex table with machine groupings and multiple data rows per machine", "columns": [{"key": "mc", "header": "M/C", "readonly": true}, {"key": "unit", "header": "Unit", "readonly": true}, {"key": "fluxTemp", "header": "Flux Temp", "readonly": false}, {"key": "preHeatBasePlate", "header": "#1 Pre heat base plate", "readonly": false}, {"key": "solderBasePlate", "header": "Solder base plate", "readonly": false}, {"key": "holdingBasePlate", "header": "#1 Holding base plate", "readonly": false}, {"key": "combinedPlates", "header": "Combined Plates", "readonly": false}, {"key": "cooling1BasePlate", "header": "#1 Cooling base plate", "readonly": false}, {"key": "cooling2BasePlate", "header": "#2 Cooling base plate", "readonly": false}], "rows": [{"id": "stringer-1", "mc": "Stringer -1", "data": [{"unit": "A", "fluxTemp": "", "preHeatBasePlate": "", "solderBasePlate": "", "holdingBasePlate": "", "combinedPlates": "", "cooling1BasePlate": "", "cooling2BasePlate": ""}, {"unit": "B", "fluxTemp": "", "preHeatBasePlate": "", "solderBasePlate": "", "holdingBasePlate": "", "combinedPlates": "", "cooling1BasePlate": "", "cooling2BasePlate": ""}]}, {"id": "stringer-2", "mc": "Stringer -2", "data": [{"unit": "A", "fluxTemp": "", "preHeatBasePlate": "", "solderBasePlate": "", "holdingBasePlate": "", "combinedPlates": "", "cooling1BasePlate": "", "cooling2BasePlate": ""}, {"unit": "B", "fluxTemp": "", "preHeatBasePlate": "", "solderBasePlate": "", "holdingBasePlate": "", "combinedPlates": "", "cooling1BasePlate": "", "cooling2BasePlate": ""}]}, {"id": "stringer-3", "mc": "Stringer -3", "data": [{"unit": "A", "fluxTemp": "", "preHeatBasePlate": "", "solderBasePlate": "", "holdingBasePlate": "", "combinedPlates": "", "cooling1BasePlate": "", "cooling2BasePlate": ""}, {"unit": "B", "fluxTemp": "", "preHeatBasePlate": "", "solderBasePlate": "", "holdingBasePlate": "", "combinedPlates": "", "cooling1BasePlate": "", "cooling2BasePlate": ""}]}]}}]}]}]}]}, {"sNo": 5, "stages": [{"stage": "Laser Tabbing & Stringing", "inspectionTypes": [{"typeOfInspection": "Aesthetics", "parameters": [{"parameter": "Machine current power", "criteria": [{"id": "5-1-1-1", "criteria": "As per laser power range 50% ± 20%", "inspectionFrequency": "Every 4 hrs", "observations": {"type": "hierarchicalTable", "description": "Complex table with machine groupings and numbered column headers", "columns": [{"key": "0", "header": null, "readonly": true}, {"key": "1", "header": "1", "readonly": false}, {"key": "2", "header": "2", "readonly": false}, {"key": "3", "header": "3", "readonly": false}, {"key": "4", "header": "4", "readonly": false}, {"key": "5", "header": "5", "readonly": false}, {"key": "6", "header": "6", "readonly": false}, {"key": "7", "header": "7", "readonly": false}, {"key": "8", "header": "8", "readonly": false}], "rows": [{"id": "front-A", "0": "Front Unit - A", "data": [{"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8"}, {"1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": ""}]}, {"id": "back-A", "0": "Back Unit - A", "data": [{"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8"}, {"1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": ""}]}, {"id": "front-B", "0": "Front Unit - B", "data": [{"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8"}, {"1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": ""}]}, {"id": "back-B", "0": "Back Unit - B", "data": [{"1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8"}, {"1": "", "2": "", "3": "", "4": "", "5": "", "6": "", "7": "", "8": ""}]}]}}]}]}]}]}, {"sNo": 7, "stages": [{"stage": "Ribbon Holder Check", "inspectionTypes": [{"typeOfInspection": "Performance", "parameters": [{"parameter": "Ribbon Holder Spring movement", "criteria": [{"id": "7-1-1-1", "criteria": "Spring up&down movement shall be free and smooth", "inspectionFrequency": "Every shift", "observations": {"type": "sectionedGrid", "description": "Grid layout with named sections/tables, each containing sub-columns", "column_nos": 3, "table_names": ["STR 1", "STR 2", "STR 3"], "columns": [{"key": "unitA", "header": "Unit-A", "readonly": false}, {"key": "unitB", "header": "Unit-B", "readonly": false}], "rows_nos": 1, "values": {"section_data": [[["", ""]], [["", ""]], [["", ""]]]}}}]}]}]}]}, {"sNo": 8, "stages": [{"stage": "Tabbing & Stringing", "inspectionTypes": [{"typeOfInspection": "Aesthetics", "parameters": [{"parameter": "STRINGER-1 to 3 Machine setup As per recipe Pre Heat Table & Soldering Temp", "criteria": [{"id": "8-1-1-1", "criteria": "Machine setup As per Reference Document VSL/FAB3/QAD/SC/12", "inspectionFrequency": "Every shift", "observations": {"type": "hierarchicalTable", "description": "Complex table with machine groupings for temperature and process data", "columns": [{"key": "mc", "header": "M/C", "readonly": true}, {"key": "unit", "header": "Unit", "readonly": true}, {"key": "fluxTemp", "header": "Flux Temp", "readonly": false}, {"key": "preHeatBasePlate", "header": "#1 Pre heat base plate", "readonly": false}, {"key": "solderBasePlate", "header": "Solder base plate", "readonly": false}, {"key": "holdingBasePlate", "header": "#1 Holding base plate", "readonly": false}, {"key": "combinedPlates", "header": "Combined Plates", "readonly": false}, {"key": "cooling1BasePlate", "header": "#1 Cooling base plate", "readonly": false}, {"key": "cooling2BasePlate", "header": "#2 Cooling base plate", "readonly": false}], "rows": [{"id": "stringer-1", "mc": "Stringer -1", "data": [{"unit": "A", "fluxTemp": "", "preHeatBasePlate": "", "solderBasePlate": "", "holdingBasePlate": "", "combinedPlates": "", "cooling1BasePlate": "", "cooling2BasePlate": ""}, {"unit": "B", "fluxTemp": "", "preHeatBasePlate": "", "solderBasePlate": "", "holdingBasePlate": "", "combinedPlates": "", "cooling1BasePlate": "", "cooling2BasePlate": ""}]}, {"id": "stringer-2", "mc": "Stringer -2", "data": [{"unit": "A", "fluxTemp": "", "preHeatBasePlate": "", "solderBasePlate": "", "holdingBasePlate": "", "combinedPlates": "", "cooling1BasePlate": "", "cooling2BasePlate": ""}, {"unit": "B", "fluxTemp": "", "preHeatBasePlate": "", "solderBasePlate": "", "holdingBasePlate": "", "combinedPlates": "", "cooling1BasePlate": "", "cooling2BasePlate": ""}]}, {"id": "stringer-3", "mc": "Stringer -3", "data": [{"unit": "A", "fluxTemp": "", "preHeatBasePlate": "", "solderBasePlate": "", "holdingBasePlate": "", "combinedPlates": "", "cooling1BasePlate": "", "cooling2BasePlate": ""}, {"unit": "B", "fluxTemp": "", "preHeatBasePlate": "", "solderBasePlate": "", "holdingBasePlate": "", "combinedPlates": "", "cooling1BasePlate": "", "cooling2BasePlate": ""}]}]}}]}]}]}]}, {"sNo": 11, "stages": [{"stage": "Auto Bussing 1", "inspectionTypes": [{"typeOfInspection": "Functionality", "parameters": [{"parameter": "Peel strength Bus Ribbon to INTC ribbon", "criteria": [{"id": "11-1-1-1", "criteria": "≥ 1.5 N (Multi BB Round wire)", "inspectionFrequency": "Every shift", "observations": {"type": "mixedInputGrid", "description": "Grid with mixed input types including checkboxes and text fields", "column_nos": 10, "rows_nos": 2, "table_fields": [{"text": "Position", "type": "text", "readonly": false}, {"text": "Top", "type": "checkbox", "readonly": false}, {"text": "Middle", "type": "checkbox", "readonly": false}, {"text": "Bottom", "type": "checkbox", "readonly": false}], "values": {"field_values": {"Position": "", "Top": false, "Middle": false, "Bottom": false}, "grid_data": [["", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", ""]]}}}]}]}]}]}, {"sNo": 19, "stages": [{"stage": "Lamination", "inspectionTypes": [{"typeOfInspection": "Aesthetics", "parameters": [{"parameter": "Laminator Machine setup As per recipe", "criteria": [{"id": "19-1-1-1", "criteria": "Lamination process parameter spec. - VSL/FAB3/QAD/SC/13", "inspectionFrequency": "Every shift", "observations": {"type": "dualTableLayout", "description": "Side-by-side table layout for equipment data with multiple sub-tables", "column_1": [{"table_name": "Laminator -1 Upper", "columns": [{"key": "chamber", "header": "Chamber", "readonly": true}, {"key": "pumping", "header": "Pumping", "readonly": false}, {"key": "pressingCooling", "header": "Pressing /Cooling", "readonly": false}, {"key": "setTemp", "header": "Set temp (˚C)", "readonly": false}], "rows": [{"id": "1", "chamber": "Chamber_1", "pumping": "", "pressingCooling": "", "setTemp": ""}, {"id": "2", "chamber": "Chamber_2", "pumping": "", "pressingCooling": "", "setTemp": ""}, {"id": "3", "chamber": "Chamber_3", "pumping": "", "pressingCooling": "", "setTemp": ""}]}, {"table_name": "Laminator -2 Upper", "columns": [{"key": "chamber", "header": "Chamber", "readonly": true}, {"key": "pumping", "header": "Pumping", "readonly": false}, {"key": "pressingCooling", "header": "Pressing /Cooling", "readonly": false}, {"key": "setTemp", "header": "Set temp (˚C)", "readonly": false}], "rows": [{"id": "1", "chamber": "Chamber_1", "pumping": "", "pressingCooling": "", "setTemp": ""}, {"id": "2", "chamber": "Chamber_2", "pumping": "", "pressingCooling": "", "setTemp": ""}, {"id": "3", "chamber": "Chamber_3", "pumping": "", "pressingCooling": "", "setTemp": ""}]}], "column_2": [{"table_name": "Laminator -1 Lower", "columns": [{"key": "chamber", "header": "Chamber", "readonly": true}, {"key": "pumping", "header": "Pumping", "readonly": false}, {"key": "pressingCooling", "header": "Pressing /Cooling", "readonly": false}, {"key": "setTemp", "header": "Set temp (˚C)", "readonly": false}], "rows": [{"id": "1", "chamber": "Chamber_1", "pumping": "", "pressingCooling": "", "setTemp": ""}, {"id": "2", "chamber": "Chamber_2", "pumping": "", "pressingCooling": "", "setTemp": ""}, {"id": "3", "chamber": "Chamber_3", "pumping": "", "pressingCooling": "", "setTemp": ""}]}, {"table_name": "Laminator -2 Lower", "columns": [{"key": "chamber", "header": "Chamber", "readonly": true}, {"key": "pumping", "header": "Pumping", "readonly": false}, {"key": "pressingCooling", "header": "Pressing /Cooling", "readonly": false}, {"key": "setTemp", "header": "Set temp (˚C)", "readonly": false}], "rows": [{"id": "1", "chamber": "Chamber_1", "pumping": "", "pressingCooling": "", "setTemp": ""}, {"id": "2", "chamber": "Chamber_2", "pumping": "", "pressingCooling": "", "setTemp": ""}, {"id": "3", "chamber": "Chamber_3", "pumping": "", "pressingCooling": "", "setTemp": ""}]}]}}]}]}]}]}, {"sNo": 8, "stages": [{"stage": "Tabbing & Stringing", "inspectionTypes": [{"typeOfInspection": "Aesthetics", "parameters": [{"parameter": "STRINGER-1 to 3 Machine setup Light Intensity & Total soldering time", "criteria": [{"id": "8-1-2-1", "criteria": "Machine setup As per Reference Document VSL/FAB3/QAD/SC/12", "inspectionFrequency": "Every shift", "observations": {"type": "hybridTableGrid", "description": "Hybrid layout combining structured table with simple grid for mixed data types", "column_1": {"columns": [{"key": "mc", "header": "M/C", "readonly": true}, {"key": "unit", "header": "Unit", "readonly": true}, {"key": "solderTemp", "header": "Solder Temp˚C", "readonly": false}, {"key": "solderTime", "header": "Solder Time ms", "readonly": false}], "rows": [{"id": "stringer-1", "mc": "Stringer -1", "data": [{"unit": "A", "solderTemp": "", "solderTime": ""}, {"unit": "B", "solderTemp": "", "solderTime": ""}]}, {"id": "stringer-2", "mc": "Stringer -2", "data": [{"unit": "A", "solderTemp": "", "solderTime": ""}, {"unit": "B", "solderTemp": "", "solderTime": ""}]}, {"id": "stringer-3", "mc": "Stringer -3", "data": [{"unit": "A", "solderTemp": "", "solderTime": ""}, {"unit": "B", "solderTemp": "", "solderTime": ""}]}]}, "column_2": {"column_nos": 12, "row_nos": 6, "columns_labels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], "values": {"grid_data": [["", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", ""]]}}}}, {"id": "8-1-2-1", "criteria": "Machine setup As per Reference Document VSL/FAB3/QAD/SC/12", "inspectionFrequency": "Every shift", "observations": {"type": "hybridTableGrid", "description": "Hybrid layout combining structured table with simple grid for mixed data types", "column_1": {"columns": [{"key": "mc", "header": "M/C", "readonly": true}, {"key": "unit", "header": "Unit", "readonly": true}, {"key": "solderTemp", "header": "Solder Temp˚C", "readonly": false}, {"key": "solderTime", "header": "Solder Time ms", "readonly": false}], "rows": [{"id": "stringer-1", "mc": "Stringer -1", "data": [{"unit": "A", "solderTemp": "", "solderTime": ""}, {"unit": "B", "solderTemp": "", "solderTime": ""}]}, {"id": "stringer-2", "mc": "Stringer -2", "data": [{"unit": "A", "solderTemp": "", "solderTime": ""}, {"unit": "B", "solderTemp": "", "solderTime": ""}]}, {"id": "stringer-3", "mc": "Stringer -3", "data": [{"unit": "A", "solderTemp": "", "solderTime": ""}, {"unit": "B", "solderTemp": "", "solderTime": ""}]}]}, "column_2": {"column_nos": 12, "row_nos": 6, "columns_labels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], "values": {"grid_data": [["", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", ""]]}}}}]}]}]}]}]}