{"Product management": {"modules": [{"moduleId": "Products_a991ecbe-579f-41f2-a2c4-f9ef59365dca", "moduleName": "Products", "moduleIcon": "products-icon", "permissions": ["Admin"], "parentModuleId": "Product management", "parentModuleName": "Product management", "moduleDescription": "Products description", "fields": [{"fieldType": "text", "basicDetails": {"fieldName": "tableId", "label": "ID", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_a59f9d95-09ce-440c-b936-a03c9199f740"}, {"fieldType": "text", "basicDetails": {"fieldName": "tableProductFamily", "label": "Product Family", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_753be61a-f5cb-47fb-b6e6-c69a3cc5595b"}, {"fieldType": "text", "basicDetails": {"fieldName": "tablePartCode", "label": "Part Code", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_75f75cf8-6b19-4405-b1d2-45b1d2827c42"}, {"fieldType": "file", "basicDetails": {"fieldName": "tableImage", "label": "Image", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": [".png", ".jpeg", ".jpg"]}, "dataSource": null, "fieldSource": null, "fieldId": "file_dd4c0e77-04cd-4065-a041-0a55c87e5f46"}, {"fieldType": "text", "basicDetails": {"fieldName": "filterModalNumber", "label": "Modal Number", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_78e4e82c-dfd1-4721-8d29-917dcfae6215"}], "forms": [{"formTitle": "Product", "formFieldsId": ["text_753be61a-f5cb-47fb-b6e6-c69a3cc5595b", "text_78e4e82c-dfd1-4721-8d29-917dcfae6215", "file_dd4c0e77-04cd-4065-a041-0a55c87e5f46"], "actionButtons": [{"buttonLabel": "Cancel", "buttonStyle": "tertiary", "actionType": "delete", "formId": null, "moduleId": null}, {"buttonLabel": "Submit", "buttonStyle": "primary", "actionType": "save", "formId": null, "moduleId": null}], "formId": "form_7714fe4c-b85e-46de-8fea-35b4674794d3"}], "lists": [{"listName": "Products", "listDescription": "list of all the products", "buttons": [{"buttonLabel": "+ New", "buttonStyle": "primary", "actionType": "dialog", "formId": "form_7714fe4c-b85e-46de-8fea-35b4674794d3", "moduleId": ""}], "tableConfig": {"columnHeaders": [{"columnId": "column_1745492903437", "columnName": "ID", "columnFieldId": "text_a59f9d95-09ce-440c-b936-a03c9199f740", "sortable": true, "dataSource": null}, {"columnId": "column_1745492910021", "columnName": "Product Family", "columnFieldId": "text_753be61a-f5cb-47fb-b6e6-c69a3cc5595b", "sortable": true, "dataSource": null}, {"columnId": "column_1745492919454", "columnName": "Part Code", "columnFieldId": "text_75f75cf8-6b19-4405-b1d2-45b1d2827c42", "sortable": true, "dataSource": null}, {"columnId": "column_1745492931355", "columnName": "Image", "columnFieldId": "file_dd4c0e77-04cd-4065-a041-0a55c87e5f46", "sortable": false, "dataSource": null}, {"columnId": "column_action_1745492954884", "columnName": "Actions", "columnFieldId": "", "sortable": false, "dataSource": [{"actionLabel": "Update", "actionIcon": "U", "actionType": "dialog", "formId": "form_7714fe4c-b85e-46de-8fea-35b4674794d3", "moduleId": null}, {"actionLabel": "Delete", "actionIcon": "D", "actionType": "delete", "formId": null, "moduleId": null, "confirmRequired": true, "confirmMessage": "Are you sure to delete this product?"}]}], "pagination": true, "rowSelection": null}, "filters": {"fieldsIds": ["text_753be61a-f5cb-47fb-b6e6-c69a3cc5595b", "text_78e4e82c-dfd1-4721-8d29-917dcfae6215"], "actionButtons": [{"buttonLabel": "Apply Filters", "buttonStyle": "primary", "actionType": "save", "formId": null, "moduleId": null}, {"buttonLabel": "Reset", "buttonStyle": "secondary", "actionType": "delete", "formId": null, "moduleId": null}]}}]}, {"moduleId": "Revisions_4e84c221-7787-4b74-9dcd-987fc4682b5c", "moduleName": "Revisions", "moduleIcon": "revisions-icon", "permissions": ["Admin"], "parentModuleId": "Product management", "parentModuleName": "Product management", "moduleDescription": "Revisions description", "fields": [{"fieldType": "text", "basicDetails": {"fieldName": "tableId", "label": "ID", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_cbfb79b8-3601-44ff-9f1a-2761a219b12b"}, {"fieldType": "text", "basicDetails": {"fieldName": "tableProductFamily", "label": "Product Family", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_af23e8ea-5659-4d20-bdec-1acd34ab1aa4"}, {"fieldType": "number", "basicDetails": {"fieldName": "tableProductRevisionNumber", "label": "Product Revision Number", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "number_b7b92fc3-801b-4c60-9e4d-60b5386ca4f2"}], "forms": [{"formTitle": "Revisions", "formFieldsId": ["text_af23e8ea-5659-4d20-bdec-1acd34ab1aa4", "number_b7b92fc3-801b-4c60-9e4d-60b5386ca4f2"], "actionButtons": [{"buttonLabel": "Cancel", "buttonStyle": "tertiary", "actionType": "delete", "formId": null, "moduleId": null}, {"buttonLabel": "Submit", "buttonStyle": "primary", "actionType": "save", "formId": null, "moduleId": null}], "formId": "form_6d69481d-054a-4950-8af3-1674c5d96c34"}], "lists": [{"listName": "Revisions", "listDescription": "products sent for revisions", "buttons": [{"buttonLabel": "+ New", "buttonStyle": "primary", "actionType": "dialog", "formId": "form_6d69481d-054a-4950-8af3-1674c5d96c34", "moduleId": ""}], "tableConfig": {"columnHeaders": [{"columnId": "column_1745493153569", "columnName": "ID", "columnFieldId": "text_cbfb79b8-3601-44ff-9f1a-2761a219b12b", "sortable": false, "dataSource": null}, {"columnId": "column_1745493160170", "columnName": "Product Family", "columnFieldId": "text_af23e8ea-5659-4d20-bdec-1acd34ab1aa4", "sortable": false, "dataSource": null}, {"columnId": "column_1745493164399", "columnName": "Product Revision Number", "columnFieldId": "number_b7b92fc3-801b-4c60-9e4d-60b5386ca4f2", "sortable": false, "dataSource": null}, {"columnId": "column_action_1745493141999", "columnName": "Actions", "columnFieldId": "", "sortable": false, "dataSource": [{"actionLabel": "Update", "actionIcon": "U", "actionType": "dialog", "formId": "form_6d69481d-054a-4950-8af3-1674c5d96c34", "moduleId": null, "confirmRequired": false}, {"actionLabel": "Delete", "actionIcon": "D", "actionType": "delete", "formId": null, "moduleId": null, "confirmRequired": true, "confirmMessage": "Are you sure to delete this product revision?"}]}], "pagination": true, "rowSelection": null}, "filters": {"fieldsIds": ["text_af23e8ea-5659-4d20-bdec-1acd34ab1aa4", "number_b7b92fc3-801b-4c60-9e4d-60b5386ca4f2"], "actionButtons": [{"buttonLabel": "Apply Filters", "buttonStyle": "primary", "actionType": "save", "formId": null, "moduleId": null}, {"buttonLabel": "Reset", "buttonStyle": "secondary", "actionType": "delete", "formId": null, "moduleId": null}]}}]}, {"moduleId": "Assemblies_7e15b54e-ec0c-4c06-af8b-28056fdd411f", "moduleName": "Assemblies", "moduleIcon": "assemblies-icon", "permissions": ["Admin"], "parentModuleId": "Product management", "parentModuleName": "Product management", "moduleDescription": "Assemblies description", "fields": [{"fieldType": "text", "basicDetails": {"fieldName": "assemblyName", "label": "Assembly Name", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_2a0ec4d9-1b3c-4b27-a540-73c165bad55d"}, {"fieldType": "text", "basicDetails": {"fieldName": "tableAssembliesModelNumber", "label": "Assembly Model Number", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_0f5c319e-1b48-4165-9810-9e9568da807c"}, {"fieldType": "text", "basicDetails": {"fieldName": "tableProductName", "label": "Product Name", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_9271b7a6-ca8a-47c0-800e-08474157141c"}, {"fieldType": "text", "basicDetails": {"fieldName": "tableModelNumber", "label": "Model Number", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_ba3087b0-3e0f-41a4-b48c-582bc9bc1f1b"}, {"fieldType": "select", "basicDetails": {"fieldName": "tableProductFamily", "label": "Product Family", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": [{"value": "thar", "label": "<PERSON><PERSON>"}, {"value": "pravegaDynamics", "label": "Pravega Dynamics"}, {"value": "bmw", "label": "BMW"}], "fieldSource": null, "fieldId": "select_a0b4006d-8f70-41ed-8409-93119f292390"}, {"fieldType": "text", "basicDetails": {"fieldName": "checkListRevision", "label": "Check List Revision", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_45f7d0a3-616a-4e0d-8a8c-9d850ff72ce5"}, {"fieldType": "number", "basicDetails": {"fieldName": "serialNumberLength", "label": "Serial Number Length", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "number_cf4041fc-2424-424d-8016-51fe549a511c"}, {"fieldType": "text", "basicDetails": {"fieldName": "parent", "label": "Parent", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_798969fd-cc5b-466c-ad39-3c83ab758a54"}, {"fieldType": "text", "basicDetails": {"fieldName": "assemblyPartCode", "label": "Assembly part code", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_cccb66a9-9167-4dd8-9397-ec7011fbcb6f"}, {"fieldType": "text", "basicDetails": {"fieldName": "tableID", "label": "ID", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_15be6d28-1014-4b08-9530-37f4a7bc7d17"}], "forms": [{"formTitle": "Assembly", "formFieldsId": ["select_a0b4006d-8f70-41ed-8409-93119f292390", "text_2a0ec4d9-1b3c-4b27-a540-73c165bad55d", "text_9271b7a6-ca8a-47c0-800e-08474157141c", "text_0f5c319e-1b48-4165-9810-9e9568da807c", "text_45f7d0a3-616a-4e0d-8a8c-9d850ff72ce5", "number_cf4041fc-2424-424d-8016-51fe549a511c", "text_798969fd-cc5b-466c-ad39-3c83ab758a54"], "actionButtons": [{"buttonLabel": "Cancel", "buttonStyle": "primary", "actionType": "delete", "formId": null, "moduleId": null}, {"buttonLabel": "Submit", "buttonStyle": "primary", "actionType": "save", "formId": null, "moduleId": null}], "formId": "form_e2cc71e4-da73-4bc6-849f-0750fe3be164"}], "lists": [{"listName": "Assemblies", "listDescription": "", "buttons": [{"buttonLabel": "+ New", "buttonStyle": "primary", "actionType": "dialog", "formId": "form_e2cc71e4-da73-4bc6-849f-0750fe3be164", "moduleId": ""}], "tableConfig": {"columnHeaders": [{"columnId": "column_1745855078915", "columnName": "ID", "columnFieldId": "text_15be6d28-1014-4b08-9530-37f4a7bc7d17", "sortable": false, "dataSource": null}, {"columnId": "column_1745855086128", "columnName": "Assembly Name", "columnFieldId": "text_2a0ec4d9-1b3c-4b27-a540-73c165bad55d", "sortable": true, "dataSource": null}, {"columnId": "column_1745855101878", "columnName": "Assembly part code", "columnFieldId": "text_cccb66a9-9167-4dd8-9397-ec7011fbcb6f", "sortable": true, "dataSource": null}, {"columnId": "column_1745855118129", "columnName": "Product Name", "columnFieldId": "text_9271b7a6-ca8a-47c0-800e-08474157141c", "sortable": true, "dataSource": null}, {"columnId": "column_1745855169179", "columnName": "Parent", "columnFieldId": "text_798969fd-cc5b-466c-ad39-3c83ab758a54", "sortable": true, "dataSource": null}, {"columnId": "column_action_1745854940182", "columnName": "Actions", "columnFieldId": "", "sortable": false, "dataSource": [{"actionLabel": "Update", "actionIcon": "U", "actionType": "dialog", "formId": "form_e2cc71e4-da73-4bc6-849f-0750fe3be164", "moduleId": null}, {"actionLabel": "Assign Revision", "actionIcon": "AR", "actionType": "save", "formId": null, "moduleId": null}, {"actionLabel": "Delete", "actionIcon": "D", "actionType": "delete", "formId": null, "moduleId": null, "confirmRequired": true, "confirmMessage": "are you sure to delete this row"}, {"actionLabel": "Cofig Flow", "actionIcon": "CF", "actionType": "save", "formId": null, "moduleId": null}]}], "pagination": true, "rowSelection": null}, "filters": {"fieldsIds": ["text_2a0ec4d9-1b3c-4b27-a540-73c165bad55d", "text_0f5c319e-1b48-4165-9810-9e9568da807c", "text_9271b7a6-ca8a-47c0-800e-08474157141c", "text_ba3087b0-3e0f-41a4-b48c-582bc9bc1f1b"], "actionButtons": [{"buttonLabel": "Apply Filters", "buttonStyle": "primary", "actionType": "save", "formId": null, "moduleId": null}, {"buttonLabel": "Reset", "buttonStyle": "secondary", "actionType": "delete", "formId": null, "moduleId": null}]}}]}, {"moduleId": "Stages_ab7f5e46-dab6-4df3-893d-4371d7a0931e", "moduleName": "Stages", "moduleIcon": "stages-icon", "permissions": ["Admin"], "parentModuleId": "Product management", "parentModuleName": "Product management", "moduleDescription": "stage description", "fields": [{"fieldType": "text", "basicDetails": {"fieldName": "tableID", "label": "ID", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_15be6d28-1014-4238-9530-37f4a7bc7d17"}, {"fieldType": "text", "basicDetails": {"fieldName": "tableProductFamily", "label": "Product Family", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_753be61a-f5cb-4j3b5-b6e6-c69a3cc5595b"}, {"fieldType": "text", "basicDetails": {"fieldName": "assemblyName", "label": "Assembly Name", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_2ashdg-sfgd-353trb27-a540-73c165bad55d"}, {"fieldType": "text", "basicDetails": {"fieldName": "tableProductName", "label": "Product Name", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_9271b7a6-345sd-47c0-800e-08474157141c"}, {"fieldType": "text", "basicDetails": {"fieldName": "assemblyName", "label": "Assembly Name", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_2a0ec4d9-1b3c-34sf-a540-73c165bad55d"}, {"fieldType": "text", "basicDetails": {"fieldName": "tableAssembliesModelNumber", "label": "Assembly Model Number", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_0f5c319e-1b48-34as-9810-9e9568da807c"}, {"fieldType": "text", "basicDetails": {"fieldName": "tablePartCode", "label": "Part Code", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_75f75cf8-6b19-43sf-b1d2-45b1d2827c42"}, {"fieldType": "text", "basicDetails": {"fieldName": "name", "label": "Name", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_6cf3caa9-dca1-491e-946f-bdc80307b77f"}, {"fieldType": "number", "basicDetails": {"fieldName": "order", "label": "Order", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "number_679cc202-01e3-4823-8023-c979889151c1"}, {"fieldType": "text", "basicDetails": {"fieldName": "assembly", "label": "Assembly", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_5d3e346f-62d9-4a8e-8eb5-388824ce958d"}], "forms": [{"formTitle": "Stage", "formFieldsId": ["text_753be61a-f5cb-4j3b5-b6e6-c69a3cc5595b", "text_5d3e346f-62d9-4a8e-8eb5-388824ce958d", "text_6cf3caa9-dca1-491e-946f-bdc80307b77f", "number_679cc202-01e3-4823-8023-c979889151c1"], "actionButtons": [{"buttonLabel": "Cancel", "buttonStyle": "secondary", "actionType": "delete", "formId": null, "moduleId": null}, {"buttonLabel": "Submit", "buttonStyle": "primary", "actionType": "save", "formId": null, "moduleId": null}], "formId": "form_113d6f9e-22ce-4bca-8af8-f23950db1dcf"}], "lists": [{"listName": "Stages", "listDescription": "", "buttons": [{"buttonLabel": "+ New", "buttonStyle": "primary", "actionType": "dialog", "formId": "form_113d6f9e-22ce-4bca-8af8-f23950db1dcf", "moduleId": ""}], "tableConfig": {"columnHeaders": [{"columnId": "column_1745857562898", "columnName": "ID", "columnFieldId": "text_15be6d28-1014-4238-9530-37f4a7bc7d17", "sortable": true, "dataSource": null}, {"columnId": "column_1745857568429", "columnName": "Name", "columnFieldId": "text_6cf3caa9-dca1-491e-946f-bdc80307b77f", "sortable": true, "dataSource": null}, {"columnId": "column_1745857583248", "columnName": "Assembly", "columnFieldId": "text_5d3e346f-62d9-4a8e-8eb5-388824ce958d", "sortable": true, "dataSource": null}, {"columnId": "column_1745857611700", "columnName": "Part Code", "columnFieldId": "text_75f75cf8-6b19-43sf-b1d2-45b1d2827c42", "sortable": true, "dataSource": null}, {"columnId": "column_action_1745857548463", "columnName": "Actions", "columnFieldId": "", "sortable": false, "dataSource": [{"actionLabel": "Update", "actionIcon": "U", "actionType": "dialog", "formId": "form_113d6f9e-22ce-4bca-8af8-f23950db1dcf", "moduleId": null}, {"actionLabel": "Delete", "actionIcon": "D", "actionType": "delete", "formId": null, "moduleId": null, "confirmRequired": true, "confirmMessage": "Are you sure to delete this?"}, {"actionLabel": "Delete incomplete inspection", "actionIcon": "D", "actionType": "delete", "formId": null, "moduleId": null, "confirmRequired": true, "confirmMessage": "Are you sure to delete this?"}]}], "pagination": true, "rowSelection": {"singleSelect": false, "multiSelect": true}}, "filters": {"fieldsIds": ["text_6cf3caa9-dca1-491e-946f-bdc80307b77f", "text_2ashdg-sfgd-353trb27-a540-73c165bad55d", "text_0f5c319e-1b48-34as-9810-9e9568da807c"], "actionButtons": [{"buttonLabel": "Apply Filters", "buttonStyle": "primary", "actionType": "save", "formId": null, "moduleId": null}, {"buttonLabel": "Reset", "buttonStyle": "secondary", "actionType": "delete", "formId": null, "moduleId": null}]}}]}, {"moduleId": "Stage_Categories_a90cc0a2-2a35-472a-99f5-9ee609b02a12", "moduleName": "Stage Categories", "moduleIcon": "stage-categories-icon", "permissions": ["Admin"], "parentModuleId": "Product management", "parentModuleName": "Product management", "moduleDescription": "Stage Categories description", "fields": [{"fieldType": "text", "basicDetails": {"fieldName": "ID", "label": "ID", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_ea0261ab-e3d6-4814-aa1a-9ec384c1be16"}, {"fieldType": "text", "basicDetails": {"fieldName": "stageCategory", "label": "Stage Category", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_9acf4b89-9021-42fe-8d55-9d39acaae413"}], "forms": [{"formTitle": "Stage", "formFieldsId": ["text_9acf4b89-9021-42fe-8d55-9d39acaae413"], "actionButtons": [{"buttonLabel": "Cancel", "buttonStyle": "secondary", "actionType": "delete", "formId": null, "moduleId": null}, {"buttonLabel": "Submit", "buttonStyle": "primary", "actionType": "save", "formId": null, "moduleId": null}], "formId": "form_928e8e69-5292-4fef-bdd0-d237af413cde"}], "lists": [{"listName": "Stage", "listDescription": "", "buttons": [{"buttonLabel": "+ New", "buttonStyle": "primary", "actionType": "dialog", "formId": "form_928e8e69-5292-4fef-bdd0-d237af413cde", "moduleId": ""}], "tableConfig": {"columnHeaders": [{"columnId": "column_1745859547847", "columnName": "ID", "columnFieldId": "text_ea0261ab-e3d6-4814-aa1a-9ec384c1be16", "sortable": false, "dataSource": null}, {"columnId": "column_1745859551611", "columnName": "Stage Category", "columnFieldId": "text_9acf4b89-9021-42fe-8d55-9d39acaae413", "sortable": false, "dataSource": null}, {"columnId": "column_action_1745859541877", "columnName": "Actions", "columnFieldId": "", "sortable": false, "dataSource": [{"actionLabel": "Update", "actionIcon": "update", "actionType": "dialog", "formId": "form_928e8e69-5292-4fef-bdd0-d237af413cde", "moduleId": null}]}], "pagination": true, "rowSelection": null}, "filters": {"fieldsIds": ["text_9acf4b89-9021-42fe-8d55-9d39acaae413"], "actionButtons": [{"buttonLabel": "Apply Filters", "buttonStyle": "primary", "actionType": "save", "formId": null, "moduleId": null}, {"buttonLabel": "Reset", "buttonStyle": "secondary", "actionType": "delete", "formId": null, "moduleId": null}]}}]}, {"moduleId": "Questions_95bbbac9-f550-47af-8295-5dd4b75cbe13", "moduleName": "Questions", "moduleIcon": "questions-icon", "permissions": ["Admin"], "parentModuleId": "Product management", "parentModuleName": "Product management", "moduleDescription": "Questions description", "fields": [], "forms": [], "lists": []}, {"moduleId": "Check_List_7715bbdb-f5e8-4d7c-81dd-95491b277a67", "moduleName": "Check List", "moduleIcon": "check-list-icon", "permissions": ["Admin"], "parentModuleId": "Product management", "parentModuleName": "Product management", "moduleDescription": "Check List description", "fields": [], "forms": [], "lists": []}, {"moduleId": "Defects_8a0750be-7056-409c-b1d4-432571d389e2", "moduleName": "Defects", "moduleIcon": "defects-icon", "permissions": ["Admin"], "parentModuleId": "Product management", "parentModuleName": "Product management", "moduleDescription": "Defects description", "fields": [], "forms": [], "lists": []}, {"moduleId": "Defect_Team_67f39978-53d8-477a-9735-fd11ec3048da", "moduleName": "Defect Team", "moduleIcon": "defect-team-icon", "permissions": ["Admin"], "parentModuleId": "Product management", "parentModuleName": "Product management", "moduleDescription": "Defect Team description", "fields": [], "forms": [], "lists": []}], "fields": [{"fieldType": "text", "basicDetails": {"fieldName": "tableId", "label": "ID", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_a59f9d95-09ce-440c-b936-a03c9199f740"}, {"fieldType": "text", "basicDetails": {"fieldName": "tableProductFamily", "label": "Product Family", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_753be61a-f5cb-47fb-b6e6-c69a3cc5595b"}, {"fieldType": "text", "basicDetails": {"fieldName": "tablePartCode", "label": "Part Code", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_75f75cf8-6b19-4405-b1d2-45b1d2827c42"}, {"fieldType": "file", "basicDetails": {"fieldName": "tableImage", "label": "Image", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": [".png", ".jpeg", ".jpg"]}, "dataSource": null, "fieldSource": null, "fieldId": "file_dd4c0e77-04cd-4065-a041-0a55c87e5f46"}, {"fieldType": "text", "basicDetails": {"fieldName": "filterModalNumber", "label": "Modal Number", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_78e4e82c-dfd1-4721-8d29-917dcfae6215"}, {"fieldType": "text", "basicDetails": {"fieldName": "tableId", "label": "ID", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_cbfb79b8-3601-44ff-9f1a-2761a219b12b"}, {"fieldType": "text", "basicDetails": {"fieldName": "tableProductFamily", "label": "Product Family", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_af23e8ea-5659-4d20-bdec-1acd34ab1aa4"}, {"fieldType": "number", "basicDetails": {"fieldName": "tableProductRevisionNumber", "label": "Product Revision Number", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "number_b7b92fc3-801b-4c60-9e4d-60b5386ca4f2"}, {"fieldType": "text", "basicDetails": {"fieldName": "assemblyName", "label": "Assembly Name", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_2a0ec4d9-1b3c-4b27-a540-73c165bad55d"}, {"fieldType": "text", "basicDetails": {"fieldName": "tableAssembliesModelNumber", "label": "Assembly Model Number", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_0f5c319e-1b48-4165-9810-9e9568da807c"}, {"fieldType": "text", "basicDetails": {"fieldName": "tableProductName", "label": "Product Name", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_9271b7a6-ca8a-47c0-800e-08474157141c"}, {"fieldType": "text", "basicDetails": {"fieldName": "tableModelNumber", "label": "Model Number", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_ba3087b0-3e0f-41a4-b48c-582bc9bc1f1b"}, {"fieldType": "select", "basicDetails": {"fieldName": "tableProductFamily", "label": "Product Family", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": [{"value": "thar", "label": "<PERSON><PERSON>"}, {"value": "pravegaDynamics", "label": "Pravega Dynamics"}, {"value": "bmw", "label": "BMW"}], "fieldSource": null, "fieldId": "select_a0b4006d-8f70-41ed-8409-93119f292390"}, {"fieldType": "text", "basicDetails": {"fieldName": "checkListRevision", "label": "Check List Revision", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_45f7d0a3-616a-4e0d-8a8c-9d850ff72ce5"}, {"fieldType": "number", "basicDetails": {"fieldName": "serialNumberLength", "label": "Serial Number Length", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "number_cf4041fc-2424-424d-8016-51fe549a511c"}, {"fieldType": "text", "basicDetails": {"fieldName": "parent", "label": "Parent", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_798969fd-cc5b-466c-ad39-3c83ab758a54"}, {"fieldType": "text", "basicDetails": {"fieldName": "assemblyPartCode", "label": "Assembly part code", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_cccb66a9-9167-4dd8-9397-ec7011fbcb6f"}, {"fieldType": "text", "basicDetails": {"fieldName": "tableID", "label": "ID", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_15be6d28-1014-4b08-9530-37f4a7bc7d17"}, {"fieldType": "text", "basicDetails": {"fieldName": "name", "label": "Name", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_6cf3caa9-dca1-491e-946f-bdc80307b77f"}, {"fieldType": "number", "basicDetails": {"fieldName": "order", "label": "Order", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "number_679cc202-01e3-4823-8023-c979889151c1"}, {"fieldType": "text", "basicDetails": {"fieldName": "assembly", "label": "Assembly", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_5d3e346f-62d9-4a8e-8eb5-388824ce958d"}, {"fieldType": "text", "basicDetails": {"fieldName": "ID", "label": "ID", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_ea0261ab-e3d6-4814-aa1a-9ec384c1be16"}, {"fieldType": "text", "basicDetails": {"fieldName": "stageCategory", "label": "Stage Category", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_9acf4b89-9021-42fe-8d55-9d39acaae413"}], "isStandalone": false, "isComponent": false}, "Employment": {"modules": [{"moduleId": "Employees_dde4f343-4fce-466e-825e-6c83c316adcd", "moduleName": "Employees", "moduleIcon": "EI", "permissions": ["Admin"], "parentModuleId": "Employment", "parentModuleName": "Employment", "moduleDescription": "Employees desc", "fields": [{"fieldType": "text", "basicDetails": {"fieldName": "employeeId", "label": "ID", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_3b7f2ac5-e9d1-4fd7-b123-af65d8e7ca31"}, {"fieldType": "text", "basicDetails": {"fieldName": "employeeName", "label": "Name", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_5fa3b2d8-6e97-4c38-85c4-16d9af207b42"}, {"fieldType": "email", "basicDetails": {"fieldName": "employeeEmail", "label": "Email", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "email_7e6d94a3-18fb-49c1-b2f5-cd8ef3b5c4d9"}, {"fieldType": "number", "basicDetails": {"fieldName": "employeeMobile", "label": "Mobile", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "number_ae71b58f-3c42-49d5-9e0a-bf2c4d18fe36"}, {"fieldType": "text", "basicDetails": {"fieldName": "employeeUserName", "label": "Employee User Name", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_9cb21ee6-45a8-4f6d-a1b3-d7ce5f8e6a24"}, {"fieldType": "select", "basicDetails": {"fieldName": "employeeRoles", "label": "Employee Roles", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": [{"value": "admin", "label": "Administrator"}, {"value": "manager", "label": "Manager"}, {"value": "supervisor", "label": "Supervisor"}, {"value": "operator", "label": "Operator"}, {"value": "quality", "label": "Quality Control"}], "fieldSource": null, "fieldId": "select_b2d3f789-51ac-4e0b-8c67-f9e5a8d7c3b2"}, {"fieldType": "text", "basicDetails": {"fieldName": "employeeFactory", "label": "Factory", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_4e7d56f8-9a23-48c5-b6f1-2dea39c8e5d7"}, {"fieldType": "text", "basicDetails": {"fieldName": "firstName", "label": "First Name", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_9d6c6aa7-2ae8-4e76-843a-e1277a6f0450"}, {"fieldType": "text", "basicDetails": {"fieldName": "lastName", "label": "Last Name", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_0afe8015-4480-4c74-a5f5-ae0de373d6a8"}, {"fieldType": "text", "basicDetails": {"fieldName": "employeeId", "label": " Employee Id", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_279976f8-e34f-4296-8615-29c24bb1807c"}, {"fieldType": "select", "basicDetails": {"fieldName": "skillLevel", "label": "Skill Level", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": [{"value": "MENTOR", "label": "MENTOR"}, {"value": "ADVANCE", "label": "ADVANCE"}, {"value": "LEARNER", "label": "LEARNER"}, {"value": "BEGINER", "label": "BEGINER"}], "fieldSource": null, "fieldId": "select_16b57135-bb9b-452e-9821-2eafbf8ca558"}], "forms": [{"formTitle": "Employee", "formFieldsId": ["text_9d6c6aa7-2ae8-4e76-843a-e1277a6f0450", "text_0afe8015-4480-4c74-a5f5-ae0de373d6a8", "email_7e6d94a3-18fb-49c1-b2f5-cd8ef3b5c4d9", "number_ae71b58f-3c42-49d5-9e0a-bf2c4d18fe36", "text_279976f8-e34f-4296-8615-29c24bb1807c", "select_b2d3f789-51ac-4e0b-8c67-f9e5a8d7c3b2", "select_16b57135-bb9b-452e-9821-2eafbf8ca558"], "actionButtons": [{"buttonLabel": "Cancel", "buttonStyle": "secondary", "actionType": "delete", "formId": null, "moduleId": null}, {"buttonLabel": "Submit", "buttonStyle": "primary", "actionType": "save", "formId": null, "moduleId": null}], "formId": "form_e4783c69-1da5-4fef-8d98-adb5e31d3031"}], "lists": [{"listName": "Employee", "listDescription": "", "buttons": [{"buttonLabel": "+ New", "buttonStyle": "primary", "actionType": "dialog", "formId": "form_e4783c69-1da5-4fef-8d98-adb5e31d3031", "moduleId": ""}], "tableConfig": {"columnHeaders": [{"columnId": "column_1745863694512", "columnName": "ID", "columnFieldId": "text_3b7f2ac5-e9d1-4fd7-b123-af65d8e7ca31", "sortable": false, "dataSource": null}, {"columnId": "column_1745863699763", "columnName": "Name", "columnFieldId": "text_5fa3b2d8-6e97-4c38-85c4-16d9af207b42", "sortable": true, "dataSource": null}, {"columnId": "column_1745863706661", "columnName": "Email", "columnFieldId": "email_7e6d94a3-18fb-49c1-b2f5-cd8ef3b5c4d9", "sortable": false, "dataSource": null}, {"columnId": "column_1745863712612", "columnName": "Mobile", "columnFieldId": "number_ae71b58f-3c42-49d5-9e0a-bf2c4d18fe36", "sortable": false, "dataSource": null}, {"columnId": "column_1745863719344", "columnName": "Employee User Name", "columnFieldId": "text_9cb21ee6-45a8-4f6d-a1b3-d7ce5f8e6a24", "sortable": true, "dataSource": null}, {"columnId": "column_1745863727212", "columnName": "Employee Roles", "columnFieldId": "select_b2d3f789-51ac-4e0b-8c67-f9e5a8d7c3b2", "sortable": true, "dataSource": null}, {"columnId": "column_1745863736044", "columnName": "Factory", "columnFieldId": "text_4e7d56f8-9a23-48c5-b6f1-2dea39c8e5d7", "sortable": true, "dataSource": null}, {"columnId": "column_action_1745863553311", "columnName": "Actions", "columnFieldId": "", "sortable": false, "dataSource": [{"actionLabel": "Update", "actionIcon": "U", "actionType": "dialog", "formId": "form_e4783c69-1da5-4fef-8d98-adb5e31d3031", "moduleId": null}, {"actionLabel": "Resend Credentials", "actionIcon": "RC", "actionType": "update", "formId": null, "moduleId": null}, {"actionLabel": "Reset Credentials", "actionIcon": "RCr", "actionType": "update", "formId": null, "moduleId": null}, {"actionLabel": "Inactive", "actionIcon": "In", "actionType": "update", "formId": null, "moduleId": null}, {"actionLabel": "Delete", "actionIcon": "D", "actionType": "delete", "formId": null, "moduleId": null, "confirmRequired": true, "confirmMessage": "Are you sure to delete this row?"}]}], "pagination": true, "rowSelection": {"singleSelect": false, "multiSelect": true}}, "filters": {"fieldsIds": ["text_5fa3b2d8-6e97-4c38-85c4-16d9af207b42", "email_7e6d94a3-18fb-49c1-b2f5-cd8ef3b5c4d9", "number_ae71b58f-3c42-49d5-9e0a-bf2c4d18fe36", "text_9cb21ee6-45a8-4f6d-a1b3-d7ce5f8e6a24"], "actionButtons": [{"buttonLabel": "Apply Filters", "buttonStyle": "primary", "actionType": "save", "formId": null, "moduleId": null}, {"buttonLabel": "Reset", "buttonStyle": "secondary", "actionType": "delete", "formId": null, "moduleId": null}]}}]}, {"moduleId": "Inactive_Employees_bfe52058-eeda-4cdd-87b7-3197cb13860b", "moduleName": "Inactive Employees", "moduleIcon": "IE", "permissions": ["Admin"], "parentModuleId": "Employment", "parentModuleName": "Employment", "moduleDescription": "Inactive Employees desc", "fields": [{"fieldType": "text", "basicDetails": {"fieldName": "employeeId", "label": "ID", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_3b7f2ac5-e3451-4fd7-b123-af65d8e7ca31"}, {"fieldType": "text", "basicDetails": {"fieldName": "employeeName", "label": "Name", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_5fa3b2d8-df43-4c38-85c4-16d9af207b42"}, {"fieldType": "email", "basicDetails": {"fieldName": "employeeEmail", "label": "Email", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "email_7e6d94a3-dfg43-49c1-b2f5-cd8ef3b5c4d9"}, {"fieldType": "number", "basicDetails": {"fieldName": "employeeMobile", "label": "Mobile", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "number_ae71b58f-34df-49d5-9e0a-bf2c4d18fe36"}, {"fieldType": "text", "basicDetails": {"fieldName": "employeeUserName", "label": "Employee User Name", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_9cb21ee6-dsf4-4f6d-a1b3-d7ce5f8e6a24"}, {"fieldType": "select", "basicDetails": {"fieldName": "employeeRoles", "label": "Employee Roles", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": [{"value": "admin", "label": "Administrator"}, {"value": "manager", "label": "Manager"}, {"value": "supervisor", "label": "Supervisor"}, {"value": "operator", "label": "Operator"}, {"value": "quality", "label": "Quality Control"}], "fieldSource": null, "fieldId": "select_b2d3f789-51ac-34rsd-8c67-f9e5a8d7c3b2"}, {"fieldType": "text", "basicDetails": {"fieldName": "employeeFactory", "label": "Factory", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_4e7d56f8-df43-48c5-b6f1-2dea39c8e5d7"}, {"fieldType": "text", "basicDetails": {"fieldName": "firstName", "label": "First Name", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_9d6c6aa7-2ae8-4e76-34fsd-e1277a6f0450"}, {"fieldType": "text", "basicDetails": {"fieldName": "lastName", "label": "Last Name", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_0afe8015-34534-4c74-a5f5-ae0de373d6a8"}, {"fieldType": "text", "basicDetails": {"fieldName": "employeeId", "label": " Employee Id", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_279976f8-e34f-4296-83415-29c24bb1807c"}, {"fieldType": "select", "basicDetails": {"fieldName": "skillLevel", "label": "Skill Level", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": [{"value": "MENTOR", "label": "MENTOR"}, {"value": "ADVANCE", "label": "ADVANCE"}, {"value": "LEARNER", "label": "LEARNER"}, {"value": "BEGINER", "label": "BEGINER"}], "fieldSource": null, "fieldId": "select_16b571345-bb9b-452e-9821-2eafbf8ca558"}], "forms": [], "lists": [{"listName": "Inactive Employees", "listDescription": "", "buttons": [{"buttonLabel": "Delete", "buttonStyle": "error", "actionType": "delete", "formId": "", "moduleId": ""}], "tableConfig": {"columnHeaders": [{"columnId": "column_1745863997452", "columnName": "ID", "columnFieldId": "text_3b7f2ac5-e3451-4fd7-b123-af65d8e7ca31", "sortable": false, "dataSource": null}, {"columnId": "column_1745864003017", "columnName": "Name", "columnFieldId": "text_5fa3b2d8-df43-4c38-85c4-16d9af207b42", "sortable": true, "dataSource": null}, {"columnId": "column_1745864010997", "columnName": "Email", "columnFieldId": "email_7e6d94a3-dfg43-49c1-b2f5-cd8ef3b5c4d9", "sortable": false, "dataSource": null}, {"columnId": "column_1745864016516", "columnName": "Mobile", "columnFieldId": "number_ae71b58f-34df-49d5-9e0a-bf2c4d18fe36", "sortable": false, "dataSource": null}, {"columnId": "column_1745864024281", "columnName": "Employee User Name", "columnFieldId": "text_9cb21ee6-dsf4-4f6d-a1b3-d7ce5f8e6a24", "sortable": true, "dataSource": null}, {"columnId": "column_1745864031763", "columnName": "Factory", "columnFieldId": "text_4e7d56f8-df43-48c5-b6f1-2dea39c8e5d7", "sortable": true, "dataSource": null}], "pagination": true, "rowSelection": null}, "filters": {"fieldsIds": ["text_5fa3b2d8-df43-4c38-85c4-16d9af207b42", "email_7e6d94a3-dfg43-49c1-b2f5-cd8ef3b5c4d9", "number_ae71b58f-34df-49d5-9e0a-bf2c4d18fe36", "text_9cb21ee6-dsf4-4f6d-a1b3-d7ce5f8e6a24"], "actionButtons": [{"buttonLabel": "Apply Filters", "buttonStyle": "primary", "actionType": "save", "formId": null, "moduleId": null}, {"buttonLabel": "Reset", "buttonStyle": "secondary", "actionType": "delete", "formId": null, "moduleId": null}]}}]}], "fields": [{"fieldType": "text", "basicDetails": {"fieldName": "employeeId", "label": "ID", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_3b7f2ac5-e9d1-4fd7-b123-af65d8e7ca31"}, {"fieldType": "text", "basicDetails": {"fieldName": "employeeName", "label": "Name", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_5fa3b2d8-6e97-4c38-85c4-16d9af207b42"}, {"fieldType": "email", "basicDetails": {"fieldName": "employeeEmail", "label": "Email", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "email_7e6d94a3-18fb-49c1-b2f5-cd8ef3b5c4d9"}, {"fieldType": "number", "basicDetails": {"fieldName": "employeeMobile", "label": "Mobile", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "number_ae71b58f-3c42-49d5-9e0a-bf2c4d18fe36"}, {"fieldType": "text", "basicDetails": {"fieldName": "employeeUserName", "label": "Employee User Name", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_9cb21ee6-45a8-4f6d-a1b3-d7ce5f8e6a24"}, {"fieldType": "select", "basicDetails": {"fieldName": "employeeRoles", "label": "Employee Roles", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": [{"value": "admin", "label": "Administrator"}, {"value": "manager", "label": "Manager"}, {"value": "supervisor", "label": "Supervisor"}, {"value": "operator", "label": "Operator"}, {"value": "quality", "label": "Quality Control"}], "fieldSource": null, "fieldId": "select_b2d3f789-51ac-4e0b-8c67-f9e5a8d7c3b2"}, {"fieldType": "text", "basicDetails": {"fieldName": "employeeFactory", "label": "Factory", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_4e7d56f8-9a23-48c5-b6f1-2dea39c8e5d7"}, {"fieldType": "text", "basicDetails": {"fieldName": "firstName", "label": "First Name", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_9d6c6aa7-2ae8-4e76-843a-e1277a6f0450"}, {"fieldType": "text", "basicDetails": {"fieldName": "lastName", "label": "Last Name", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_0afe8015-4480-4c74-a5f5-ae0de373d6a8"}, {"fieldType": "text", "basicDetails": {"fieldName": "employeeId", "label": " Employee Id", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_279976f8-e34f-4296-8615-29c24bb1807c"}, {"fieldType": "select", "basicDetails": {"fieldName": "skillLevel", "label": "Skill Level", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": [{"value": "MENTOR", "label": "MENTOR"}, {"value": "ADVANCE", "label": "ADVANCE"}, {"value": "LEARNER", "label": "LEARNER"}, {"value": "BEGINER", "label": "BEGINER"}], "fieldSource": null, "fieldId": "select_16b57135-bb9b-452e-9821-2eafbf8ca558"}, {"fieldType": "text", "basicDetails": {"fieldName": "employeeId", "label": "ID", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_3b7f2ac5-e3451-4fd7-b123-af65d8e7ca31"}, {"fieldType": "text", "basicDetails": {"fieldName": "employeeName", "label": "Name", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_5fa3b2d8-df43-4c38-85c4-16d9af207b42"}, {"fieldType": "email", "basicDetails": {"fieldName": "employeeEmail", "label": "Email", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "email_7e6d94a3-dfg43-49c1-b2f5-cd8ef3b5c4d9"}, {"fieldType": "number", "basicDetails": {"fieldName": "employeeMobile", "label": "Mobile", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "number_ae71b58f-34df-49d5-9e0a-bf2c4d18fe36"}, {"fieldType": "text", "basicDetails": {"fieldName": "employeeUserName", "label": "Employee User Name", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_9cb21ee6-dsf4-4f6d-a1b3-d7ce5f8e6a24"}, {"fieldType": "select", "basicDetails": {"fieldName": "employeeRoles", "label": "Employee Roles", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": [{"value": "admin", "label": "Administrator"}, {"value": "manager", "label": "Manager"}, {"value": "supervisor", "label": "Supervisor"}, {"value": "operator", "label": "Operator"}, {"value": "quality", "label": "Quality Control"}], "fieldSource": null, "fieldId": "select_b2d3f789-51ac-34rsd-8c67-f9e5a8d7c3b2"}, {"fieldType": "text", "basicDetails": {"fieldName": "employeeFactory", "label": "Factory", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_4e7d56f8-df43-48c5-b6f1-2dea39c8e5d7"}, {"fieldType": "text", "basicDetails": {"fieldName": "firstName", "label": "First Name", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_9d6c6aa7-2ae8-4e76-34fsd-e1277a6f0450"}, {"fieldType": "text", "basicDetails": {"fieldName": "lastName", "label": "Last Name", "required": true, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_0afe8015-34534-4c74-a5f5-ae0de373d6a8"}, {"fieldType": "text", "basicDetails": {"fieldName": "employeeId", "label": " Employee Id", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_279976f8-e34f-4296-83415-29c24bb1807c"}, {"fieldType": "select", "basicDetails": {"fieldName": "skillLevel", "label": "Skill Level", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": [{"value": "MENTOR", "label": "MENTOR"}, {"value": "ADVANCE", "label": "ADVANCE"}, {"value": "LEARNER", "label": "LEARNER"}, {"value": "BEGINER", "label": "BEGINER"}], "fieldSource": null, "fieldId": "select_16b571345-bb9b-452e-9821-2eafbf8ca558"}], "isStandalone": false, "isComponent": false}, "Audit": {"modules": [{"moduleId": "sectors_15ce2d20-e726-41ee-a414-0258dbeab2e5", "moduleName": "Sectors", "moduleIcon": "S", "permissions": ["Admin"], "parentModuleId": "Audit", "parentModuleName": "Audit", "moduleDescription": "Sectors desc", "fields": [{"fieldType": "text", "basicDetails": {"fieldName": "id", "label": "ID", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_5e9a46dd-a8b4-44c8-b73d-bfc36d64471f"}, {"fieldType": "text", "basicDetails": {"fieldName": "name", "label": "Name", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_7af305c8-75ae-4230-b5df-e551e8cbbc66"}], "forms": [{"formTitle": "Sectors", "formFieldsId": ["text_7af305c8-75ae-4230-b5df-e551e8cbbc66"], "actionButtons": [{"buttonLabel": "Cancel", "buttonStyle": "secondary", "actionType": "delete", "formId": null, "moduleId": null}, {"buttonLabel": "Submit", "buttonStyle": "primary", "actionType": "save", "formId": null, "moduleId": null}], "formId": "form_3f79ca62-6fef-4dcd-87c9-04fc42167025"}], "lists": [{"listName": "Sector", "listDescription": "", "buttons": [{"buttonLabel": "+ New", "buttonStyle": "primary", "actionType": "dialog", "formId": "form_3f79ca62-6fef-4dcd-87c9-04fc42167025", "moduleId": ""}], "tableConfig": {"columnHeaders": [{"columnId": "column_1745867318184", "columnName": "ID", "columnFieldId": "text_5e9a46dd-a8b4-44c8-b73d-bfc36d64471f", "sortable": false, "dataSource": null}, {"columnId": "column_1745867324271", "columnName": "Name", "columnFieldId": "text_7af305c8-75ae-4230-b5df-e551e8cbbc66", "sortable": true, "dataSource": null}, {"columnId": "column_action_1745867310635", "columnName": "Actions", "columnFieldId": "", "sortable": false, "dataSource": [{"actionLabel": "Update", "actionIcon": "U", "actionType": "dialog", "formId": "form_3f79ca62-6fef-4dcd-87c9-04fc42167025", "moduleId": null}, {"actionLabel": "Delete", "actionIcon": "D", "actionType": "delete", "formId": null, "moduleId": null, "confirmRequired": true, "confirmMessage": "Are you sure to delete this row?"}]}], "pagination": true, "rowSelection": null}, "filters": {"fieldsIds": ["text_7af305c8-75ae-4230-b5df-e551e8cbbc66"], "actionButtons": [{"buttonLabel": "Apply Filters", "buttonStyle": "primary", "actionType": "save", "formId": null, "moduleId": null}, {"buttonLabel": "Reset", "buttonStyle": "secondary", "actionType": "delete", "formId": null, "moduleId": null}]}}]}, {"moduleId": "process_audit_area_3509599f-1cdd-437b-8af0-2be5d1984dd9", "moduleName": "Process Audit Area", "moduleIcon": "PAA", "permissions": ["Admin"], "parentModuleId": "Audit", "parentModuleName": "Audit", "moduleDescription": "Process Audit Area desc", "fields": [{"fieldType": "text", "basicDetails": {"fieldName": "id", "label": "ID", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_e0e7f0d8-f5b0-4851-9b53-05e778265342"}, {"fieldType": "text", "basicDetails": {"fieldName": "name", "label": "Name", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_2b8d7a3d-3592-4a01-97a3-c35dbaa18def"}], "forms": [{"formTitle": "Audit Area", "formFieldsId": ["text_2b8d7a3d-3592-4a01-97a3-c35dbaa18def"], "actionButtons": [{"buttonLabel": "Cancel", "buttonStyle": "secondary", "actionType": "delete", "formId": null, "moduleId": null}, {"buttonLabel": "Submit", "buttonStyle": "primary", "actionType": "save", "formId": null, "moduleId": null}], "formId": "form_cb3b0ba9-29ba-4156-9596-afbf44619f73"}], "lists": [{"listName": "Audit Area", "listDescription": "", "buttons": [{"buttonLabel": "+ New", "buttonStyle": "primary", "actionType": "dialog", "formId": "form_cb3b0ba9-29ba-4156-9596-afbf44619f73", "moduleId": ""}], "tableConfig": {"columnHeaders": [{"columnId": "column_1745867610749", "columnName": "ID", "columnFieldId": "text_e0e7f0d8-f5b0-4851-9b53-05e778265342", "sortable": false, "dataSource": null}, {"columnId": "column_1745867616599", "columnName": "Name", "columnFieldId": "text_2b8d7a3d-3592-4a01-97a3-c35dbaa18def", "sortable": true, "dataSource": null}, {"columnId": "column_action_1745867638235", "columnName": "Actions", "columnFieldId": "", "sortable": false, "dataSource": [{"actionLabel": "Update", "actionIcon": "U", "actionType": "dialog", "formId": "form_cb3b0ba9-29ba-4156-9596-afbf44619f73", "moduleId": null}, {"actionLabel": "Delete", "actionIcon": "D", "actionType": "delete", "formId": null, "moduleId": null, "confirmRequired": true, "confirmMessage": "Are you sure to delete this?"}]}], "pagination": true, "rowSelection": null}, "filters": {"fieldsIds": ["text_2b8d7a3d-3592-4a01-97a3-c35dbaa18def"], "actionButtons": [{"buttonLabel": "Apply Filters", "buttonStyle": "primary", "actionType": "save", "formId": null, "moduleId": null}, {"buttonLabel": "Reset", "buttonStyle": "secondary", "actionType": "delete", "formId": null, "moduleId": null}]}}]}, {"moduleId": "process_audit_category_f4b6e001-4fe8-4eae-b325-d0380db4da42", "moduleName": "Process Audit Category", "moduleIcon": "PAC", "permissions": ["Admin"], "parentModuleId": "Audit", "parentModuleName": "Audit", "moduleDescription": "Process Audit Category desc", "fields": [{"fieldType": "text", "basicDetails": {"fieldName": "id", "label": "ID", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_63b3177b-be44-44ac-a078-bf0fbecd0d9f"}, {"fieldType": "text", "basicDetails": {"fieldName": "name", "label": "Name", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_d2a6f556-8051-4513-9327-fdf1692c31a1"}, {"fieldType": "text", "basicDetails": {"fieldName": "auditArea", "label": "Audit Area", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_503a1474-c626-4628-a912-0c299f7c867e"}], "forms": [{"formTitle": "Audit Category", "formFieldsId": ["text_503a1474-c626-4628-a912-0c299f7c867e", "text_d2a6f556-8051-4513-9327-fdf1692c31a1"], "actionButtons": [{"buttonLabel": "Cancel", "buttonStyle": "secondary", "actionType": "delete", "formId": null, "moduleId": null}, {"buttonLabel": "Submit", "buttonStyle": "primary", "actionType": "save", "formId": null, "moduleId": null}], "formId": "form_e9c71cd2-b170-451e-80ef-5c1fac2d5f54"}], "lists": [{"listName": "Process Audit Category", "listDescription": "", "buttons": [{"buttonLabel": "+ New", "buttonStyle": "primary", "actionType": "dialog", "formId": "form_e9c71cd2-b170-451e-80ef-5c1fac2d5f54", "moduleId": ""}], "tableConfig": {"columnHeaders": [{"columnId": "column_1745898495021", "columnName": "ID", "columnFieldId": "text_63b3177b-be44-44ac-a078-bf0fbecd0d9f", "sortable": false, "dataSource": null}, {"columnId": "column_1745898500174", "columnName": "Name", "columnFieldId": "text_d2a6f556-8051-4513-9327-fdf1692c31a1", "sortable": false, "dataSource": null}, {"columnId": "column_1745898505272", "columnName": "Audit Area", "columnFieldId": "text_503a1474-c626-4628-a912-0c299f7c867e", "sortable": false, "dataSource": null}, {"columnId": "column_action_1745869120021", "columnName": "Actions", "columnFieldId": "", "sortable": false, "dataSource": [{"actionLabel": "Update", "actionIcon": "U", "actionType": "dialog", "formId": "form_e9c71cd2-b170-451e-80ef-5c1fac2d5f54", "moduleId": null}, {"actionLabel": "Delete", "actionIcon": "D", "actionType": "delete", "formId": null, "moduleId": null, "confirmRequired": true, "confirmMessage": "Are you sure you want to delete this row?"}]}], "pagination": true, "rowSelection": null}, "filters": {"fieldsIds": ["text_d2a6f556-8051-4513-9327-fdf1692c31a1"], "actionButtons": [{"buttonLabel": "Apply Filters", "buttonStyle": "primary", "actionType": "save", "formId": null, "moduleId": null}, {"buttonLabel": "Reset", "buttonStyle": "secondary", "actionType": "delete", "formId": null, "moduleId": null}]}}]}], "fields": [{"fieldType": "text", "basicDetails": {"fieldName": "id", "label": "ID", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_5e9a46dd-a8b4-44c8-b73d-bfc36d64471f"}, {"fieldType": "text", "basicDetails": {"fieldName": "name", "label": "Name", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_7af305c8-75ae-4230-b5df-e551e8cbbc66"}, {"fieldType": "text", "basicDetails": {"fieldName": "id", "label": "ID", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_e0e7f0d8-f5b0-4851-9b53-05e778265342"}, {"fieldType": "text", "basicDetails": {"fieldName": "name", "label": "Name", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_2b8d7a3d-3592-4a01-97a3-c35dbaa18def"}, {"fieldType": "text", "basicDetails": {"fieldName": "id", "label": "ID", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_63b3177b-be44-44ac-a078-bf0fbecd0d9f"}, {"fieldType": "text", "basicDetails": {"fieldName": "name", "label": "Name", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_d2a6f556-8051-4513-9327-fdf1692c31a1"}, {"fieldType": "text", "basicDetails": {"fieldName": "auditArea", "label": "Audit Area", "required": false, "placeholder": "", "errorMessage": ""}, "validation": {"pattern": null, "minValue": null, "maxValue": null, "minLength": null, "maxLength": null, "minDate": null, "maxDate": null, "uploadFileTypes": null}, "dataSource": null, "fieldSource": null, "fieldId": "text_503a1474-c626-4628-a912-0c299f7c867e"}], "isStandalone": false, "isComponent": false}, "skldjf": {"modules": [{"moduleId": "skldjf_73efeab2-2ab9-4833-b822-0851f800391f", "moduleName": "Skldjf", "moduleIcon": "skldjf-icon", "permissions": ["Admin"], "parentModuleId": null, "parentModuleName": null, "moduleDescription": "dskjlfsj", "fields": [], "forms": [], "lists": []}], "fields": [], "isStandalone": true, "isComponent": true}}