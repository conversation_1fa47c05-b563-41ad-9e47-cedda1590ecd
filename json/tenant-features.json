{"tenant_features": {"tenant_1": {"tenant_id": 1, "tenant_name": "Acme Manufacturing Corp", "tenant_type": "ROLE_ORGANIZATION_ADMIN", "assigned_features": [{"feature_id": "product_management", "feature_name": "Product Management", "enabled": true, "assigned_date": "2024-01-15T10:00:00Z", "expiry_date": null, "description": "Complete product lifecycle management including products, revisions, assemblies, and stages"}, {"feature_id": "audits", "feature_name": "Audits", "enabled": true, "assigned_date": "2024-01-20T10:00:00Z", "expiry_date": null, "description": "Process audits, 5S audits, and DIB audits management"}, {"feature_id": "calibration", "feature_name": "Calibration", "enabled": true, "assigned_date": "2024-02-01T10:00:00Z", "expiry_date": "2024-12-31T23:59:59Z", "description": "Equipment calibration management and tracking"}]}, "tenant_2": {"tenant_id": 2, "tenant_name": "Beta Industries Ltd", "tenant_type": "ROLE_FACTORY_ADMIN", "assigned_features": [{"feature_id": "product_management", "feature_name": "Product Management", "enabled": true, "assigned_date": "2024-01-25T10:00:00Z", "expiry_date": null, "description": "Basic product management features"}, {"feature_id": "quality_control", "feature_name": "Quality Control", "enabled": true, "assigned_date": "2024-02-10T10:00:00Z", "expiry_date": null, "description": "Quality control and inspection management"}]}}, "feature_definitions": {"product_management": {"feature_name": "Product Management", "description": "Complete product lifecycle management", "modules": ["Products", "Revisions", "Assemblies", "Stages", "Stage_Categories", "Questions", "Check_List", "Defects", "Defect_Team"], "parent_module_name": "Product management"}, "audits": {"feature_name": "Audits", "description": "Audit management system", "modules": ["Process_Audits", "Five_S_Audits", "DIB_Audits", "Audit_Areas", "Audit_Questions"], "parent_module_name": "Audits"}, "calibration": {"feature_name": "Calibration", "description": "Equipment calibration management", "modules": ["Calibration"], "parent_module_name": "Calibration"}, "quality_control": {"feature_name": "Quality Control", "description": "Quality control and inspection", "modules": ["Inspections", "Quality_Reports", "Non_Conformance"], "parent_module_name": "Quality Control"}}}